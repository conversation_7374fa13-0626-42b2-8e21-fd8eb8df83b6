package sync

import (
	"fmt"
	"gopkg.in/yaml.v2"
	. "mysql_archive/pkg/config"
	"mysql_archive/pkg/utils"
	"mysql_archive/pkg/zlog"
	"strings"
)

func RemoteSyncConfig() bool {
	zlog.Info("【配置同步】==================== 开始同步远程配置 ====================")
	zlog.Debug("【配置同步】步骤1: 进入RemoteSyncConfig方法")

	// 1.1 读取远程配置
	zlog.Debug("【配置同步】步骤2: 获取本地IP地址")
	ip := utils.GetLocalIP()
	if ip == "" {
		zlog.Error("【配置同步】步骤2: 获取本地IP失败 ")
		zlog.Error("【配置同步】==================== 配置同步失败 ====================")
		return false
	}
	zlog.Debug("【配置同步】步骤2: 获取本地IP成功 , IP:", ip)

	zlog.Debug("【配置同步】步骤3: 构建配置请求URL")
	configURL := fmt.Sprintf("%s/nacosGetConfig/ContextPath/service/backupinfo?ip=%s", NacosUrl, ip)
	zlog.Debug("【配置同步】步骤3: URL构建完成 ")
	zlog.Debug("【配置同步】配置URL:", configURL)

	zlog.Debug("【配置同步】步骤4: 开始请求远程配置")
	backupinfo := GetConfig(configURL)

	// 获取不到配置就退出
	if backupinfo == nil {
		zlog.Error("【配置同步】步骤4: 获取远程配置失败 ")
		zlog.Error("【配置同步】失败原因: HTTP请求返回空数据")
		zlog.Error("【配置同步】请求URL:", configURL)
		zlog.Error("【配置同步】==================== 配置同步失败 ====================")
		return false
	}
	zlog.Debug("【配置同步】步骤4: 获取远程配置成功 ")
	zlog.Debug("【配置同步】配置数据大小:", len(backupinfo), "字节")

	// 显示配置内容预览
	if len(backupinfo) > 200 {
		zlog.Debug("【配置同步】配置内容预览:", string(backupinfo))
	} else {
		zlog.Debug("【配置同步】配置内容:", string(backupinfo))
	}

	// 配置持久化
	zlog.Debug("【配置同步】步骤5: 开始持久化备份配置信息")
	Role.Store("backupinfo", backupinfo)
	zlog.Debug("【配置同步】步骤5: 备份配置信息持久化完成 ")

	// 如果配置存在
	zlog.Debug("【配置同步】步骤6: 验证配置缓存")

	// 定义计数器变量
	successCount := 0
	failCount := 0

	if body, ok := Role.Load("backupinfo"); ok {
		// 检查类型，确保是[]byte类型的配置数据
		if bodyBytes, isByteSlice := body.([]byte); isByteSlice {
			zlog.Debug("【配置同步】步骤6: 从缓存加载备份配置成功 ")

			// 表结构持久化
			zlog.Debug("【配置同步】步骤7: 开始解析YAML配置")
			var configMap = make(map[string]map[string]string)
			err := yaml.Unmarshal(bodyBytes, configMap)
			if err != nil {
				zlog.Error("【配置同步】步骤7: 解析备份配置YAML失败 ")
				zlog.Error("【配置同步】失败原因:", err.Error())
				zlog.Error("【配置同步】原始配置长度:", len(bodyBytes))
				if len(bodyBytes) > 500 {
					zlog.Error("【配置同步】原始配置内容预览:", string(bodyBytes[:500]), "...")
				} else {
					zlog.Error("【配置同步】原始配置内容:", string(bodyBytes))
				}
				zlog.Error("【配置同步】==================== 配置同步失败 ====================")
				return false
			}
			zlog.Debug("【配置同步】步骤7: 解析备份配置成功 ")
			zlog.Info("【配置同步】解析结果: 发现", len(configMap), "个表配置")

			// 列出所有表名
			tableNames := make([]string, 0, len(configMap))
			for tableName := range configMap {
				tableNames = append(tableNames, tableName)
			}
			zlog.Debug("【配置同步】表列表:", tableNames)

			// 获取每个表的结构信息
			zlog.Debug("【配置同步】步骤8: 开始获取各表结构信息")

			for i, tn := range tableNames {
				zlog.Debug("【配置同步】步骤8.", i+1, ": 处理表", tn, "(", i+1, "/", len(tableNames), ")")

				// 构建表结构请求URL
				schemaURL := fmt.Sprintf("%s/nacosGetConfig/ContextPath/service/tableschme?tablename=%s", NacosUrl, tn)
				zlog.Debug("【配置同步】表结构URL:", schemaURL)

				// 检查缓存中是否已有旧的表结构
				zlog.Debug("【配置同步】步骤8.", i+1, ".1: 检查缓存中的旧表结构")
				var oldSchema []byte
				if oldBody, exists := Role.Load(tn); exists {
					// 检查类型，确保是[]byte类型的表结构数据
					if oldBodyBytes, isByteSlice := oldBody.([]byte); isByteSlice {
						oldSchema = oldBodyBytes
						zlog.Debug("【配置同步】发现缓存中的旧表结构 , 表名:", tn, "旧结构长度:", len(oldSchema))
					} else {
						zlog.Debug("【配置同步】缓存中的值类型错误, 表名:", tn, "实际类型:", fmt.Sprintf("%T", oldBody))
					}
				} else {
					zlog.Debug("【配置同步】缓存中无此表结构, 表名:", tn)
				}

				zlog.Debug("【配置同步】步骤8.", i+1, ".2: 请求最新表结构")
				body := GetConfig(schemaURL)
				if body != nil {
					zlog.Debug("【配置同步】步骤8.", i+1, ".2: 获取表结构成功 , 表名:", tn)

					// 比较新旧结构是否有变化
					zlog.Debug("【配置同步】步骤8.", i+1, ".3: 比较表结构变化")
					if oldSchema != nil && string(oldSchema) == string(body) {
						zlog.Debug("【配置同步】表结构无变化 , 表名:", tn, "结构长度:", len(body))
					} else {
						zlog.Info("【配置同步】检测到表结构变化！, 表名:", tn)
						if oldSchema != nil {
							zlog.Debug("【配置同步】结构对比 - 旧长度:", len(oldSchema), "新长度:", len(body))
						} else {
							zlog.Debug("【配置同步】首次获取表结构，长度:", len(body))
						}

						// 显示新结构的关键字段信息
						bodyStr := string(body)
						keyFields := []string{}
						if strings.Contains(bodyStr, "n_amount2") {
							keyFields = append(keyFields, "n_amount2")
						}
						if strings.Contains(bodyStr, "n_amount1") {
							keyFields = append(keyFields, "n_amount1")
						}
						if strings.Contains(bodyStr, "vc_license1") {
							keyFields = append(keyFields, "vc_license1")
						}
						if len(keyFields) > 0 {
							zlog.Debug("【配置同步】新结构包含关键字段:", keyFields, "表名:", tn)
						}
					}

					zlog.Debug("【配置同步】步骤8.", i+1, ".4: 更新表结构缓存")
					Role.Store(tn, body)
					zlog.Debug("【配置同步】表结构更新到缓存成功 , 表名:", tn, "结构长度:", len(body))
					successCount++
				} else {
					zlog.Error("【配置同步】步骤8.", i+1, ".2: 获取表结构失败 , 表名:", tn)
					zlog.Error("【配置同步】失败URL:", schemaURL)
					failCount++
				}
			}

			zlog.Debug("【配置同步】步骤8: 表结构获取完成")
			zlog.Info("【配置同步】成功:", successCount, "个表, 失败:", failCount, "个表")
			zlog.Debug("【配置同步】步骤9: 设置同步状态")
			if failCount == 0 {
				Re.Status = 0
				Re.StatusMap["RemoteSyncConfig"] = 0
				zlog.Debug("【配置同步】步骤9: 状态设置为成功 ")
				zlog.Info("【配置同步】所有表结构同步成功，总计:", successCount, "个表")
				zlog.Info("【配置同步】==================== 配置同步成功 ====================")
			} else {
				Re.Status = ReadConfig
				Re.StatusMap["RemoteSyncConfig"] = ReadConfig
				zlog.Warn("【配置同步】步骤9: 状态设置为部分失败 ")
				zlog.Warn("【配置同步】成功:", successCount, "个表, 失败:", failCount, "个表")
				zlog.Warn("【配置同步】==================== 配置同步部分失败 ====================")
			}
		} else {
			// 缓存中的值不是[]byte类型
			zlog.Error("【配置同步】步骤6: 缓存中的值类型错误 ")
			zlog.Error("【配置同步】键 backupinfo 对应的值不是配置数据（[]byte类型）")
			zlog.Error("【配置同步】实际类型:", fmt.Sprintf("%T", body))
			Re.Status = ReadConfig
			Re.StatusMap["RemoteSyncConfig"] = ReadConfig
			return false
		}
	} else {
		zlog.Error("【配置同步】步骤6: 从缓存加载备份配置失败 ")
		zlog.Error("【配置同步】失败原因: backupinfo不存在于Role缓存中")
		Re.Status = ReadConfig
		Re.StatusMap["RemoteSyncConfig"] = ReadConfig
		zlog.Error("【配置同步】==================== 配置同步失败 ====================")
		return false
	}

	zlog.Debug("【配置同步】步骤10: RemoteSyncConfig方法执行完成")
	if failCount == 0 {
		zlog.Info("【配置同步】最终结果: 完全成功 ")
		return true
	} else {
		zlog.Warn("【配置同步】最终结果: 部分成功 ")
		return successCount > 0 // 只要有成功的就返回true
	}
}

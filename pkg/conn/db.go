package conn

import (
	"database/sql"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	. "mysql_archive/pkg/config"
	"mysql_archive/pkg/utils"
	"mysql_archive/pkg/zlog"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
	"log"
	"io"
	"gopkg.in/natefinch/lumberjack.v2"
)

func makeEmptyStr() *sql.NullString {
	var s sql.NullString
	return &s
}

// normalizeSchema 标准化表结构字符串，用于准确对比
func normalizeSchema(schema string) string {
	if schema == "" {
		return ""
	}

	zlog.Debug("【标准化】开始标准化表结构")
	zlog.Debug("【标准化】原始长度:", len(schema), "字节")

	// 1. 移除首尾空白字符
	schema = strings.TrimSpace(schema)

	// 2. 统一换行符为 \n
	schema = strings.ReplaceAll(schema, "\r\n", "\n")
	schema = strings.ReplaceAll(schema, "\r", "\n")

	// 3. 按行处理
	lines := strings.Split(schema, "\n")
	var normalizedLines []string

	for i, line := range lines {
		originalLine := line
		// 移除行首尾空白
		line = strings.TrimSpace(line)

		zlog.Debug("【标准化调试】处理第", i+1, "行:")
		zlog.Debug("【标准化调试】  原始:", originalLine)
		zlog.Debug("【标准化调试】  去空白:", line)

		// 跳过空行
		if line == "" {
			zlog.Debug("【标准化调试】  结果: 跳过空行")
			continue
		}

		// 标准化多个连续空格为单个空格
		re := regexp.MustCompile(`\s+`)
		beforeSpaceNorm := line
		line = re.ReplaceAllString(line, " ")
		if beforeSpaceNorm != line {
			zlog.Debug("【标准化调试】  空格标准化:", beforeSpaceNorm, "->", line)
		}

		// 移除注释后的多余空格（保留注释内容，但标准化格式）
		if strings.Contains(line, "COMMENT") {
			beforeCommentNorm := line
			// 标准化 COMMENT 前后的空格
			line = regexp.MustCompile(`\s+COMMENT\s+`).ReplaceAllString(line, " COMMENT ")
			if beforeCommentNorm != line {
				zlog.Debug("【标准化调试】  注释标准化:", beforeCommentNorm, "->", line)
			}
		}

		normalizedLines = append(normalizedLines, line)
		zlog.Debug("【标准化调试】  最终结果:", line)
	}

	// 4. 重新组合，确保每行结尾一致
	result := strings.Join(normalizedLines, "\n")

	zlog.Debug("【标准化】标准化后长度:", len(result), "字节")
	zlog.Debug("【标准化】处理了", len(lines), "行，保留", len(normalizedLines), "行")

	return result
}

// parseFieldOrder 解析CREATE TABLE语句中的字段顺序
func parseFieldOrder(createTableSQL string) []string {
	zlog.Debug("【字段解析】开始解析字段顺序")

	var fieldOrder []string
	lines := strings.Split(createTableSQL, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过空行、注释行、CREATE TABLE行、索引定义等
		if line == "" ||
			strings.HasPrefix(line, "--") ||
			strings.HasPrefix(line, "/*") ||
			strings.Contains(line, "CREATE TABLE") ||
			strings.Contains(line, "PRIMARY KEY") ||
			strings.Contains(line, "UNIQUE KEY") ||
			strings.Contains(line, "KEY ") ||
			strings.Contains(line, "INDEX ") ||
			line == ")" ||
			strings.HasPrefix(line, ")") {
			continue
		}

		// 提取字段名（在反引号之间的内容）
		if strings.Contains(line, "`") {
			start := strings.Index(line, "`")
			if start != -1 {
				end := strings.Index(line[start+1:], "`")
				if end != -1 {
					fieldName := line[start+1 : start+1+end]
					fieldOrder = append(fieldOrder, fieldName)
					zlog.Debug("【字段解析】第", len(fieldOrder), "个字段:", fieldName, "行号:", i+1)
				}
			}
		}
	}

	zlog.Debug("【字段解析】解析完成，共", len(fieldOrder), "个字段")
	zlog.Debug("【字段解析】字段顺序:", fieldOrder)

	return fieldOrder
}

// findFieldPosition 找到字段应该插入的位置
func findFieldPosition(targetField string, fieldOrder []string) (position string, found bool) {
	zlog.Debug("【位置查找】查找字段位置:", targetField)

	for i, field := range fieldOrder {
		if field == targetField {
			if i == 0 {
				// 第一个字段
				position = "FIRST"
				zlog.Debug("【位置查找】字段应插入到最前面")
			} else {
				// 在某个字段之后
				prevField := fieldOrder[i-1]
				position = fmt.Sprintf("AFTER `%s`", prevField)
				zlog.Debug("【位置查找】字段应插入到", prevField, "之后")
			}
			found = true
			return
		}
	}

	// 如果没找到，说明是新字段，插入到最后
	if len(fieldOrder) > 0 {
		lastField := fieldOrder[len(fieldOrder)-1]
		position = fmt.Sprintf("AFTER `%s`", lastField)
		zlog.Debug("【位置查找】新字段，插入到最后一个字段", lastField, "之后")
	} else {
		position = "FIRST"
		zlog.Debug("【位置查找】表为空，插入到最前面")
	}

	found = false
	return
}

type Conn struct {
	Db *sql.DB
}

var (
	// DB 对外使用
	DB     *sql.DB
	Yyyymm string
)

type Manage struct {
	// 记录返回结果
	base map[string]string
	// 记录列名顺序
	keys []string
}

func init() {
	Yyyymm = time.Now().Format("200601")

	// 配置MySQL驱动日志输出到文件而不是控制台
	setupMySQLDriverLogging()
}

// setupMySQLDriverLogging 配置MySQL驱动的日志输出
func setupMySQLDriverLogging() {
	// 创建日志目录
	if _, err := os.Stat("log"); os.IsNotExist(err) {
		os.MkdirAll("log", 0755)
	}

	// 创建MySQL驱动专用的日志文件
	mysqlLogFile := &lumberjack.Logger{
		Filename:   "log/mysql_driver.log", // MySQL驱动日志文件
		MaxSize:    100,                    // 每个日志文件最大100MB
		MaxBackups: 3,                      // 保留3个备份文件
		MaxAge:     7,                      // 保留7天
		Compress:   true,                   // 压缩旧文件
	}

	// 创建一个自定义的Writer，将MySQL驱动日志转换为错误级别
	mysqlLogWriter := &MySQLLogWriter{
		logFile: mysqlLogFile,
	}

	// 设置MySQL驱动的日志输出
	log.SetOutput(mysqlLogWriter)
	log.SetFlags(log.LstdFlags) // 设置时间戳格式
}

// MySQLLogWriter 自定义的日志写入器，将MySQL驱动日志写入文件并设置为错误级别
type MySQLLogWriter struct {
	logFile io.Writer
}

func (w *MySQLLogWriter) Write(p []byte) (n int, err error) {
	// 将MySQL驱动的日志内容写入专用日志文件
	logContent := string(p)

	// 检查是否是连接相关的错误日志
	if strings.Contains(logContent, "closing bad idle connection") ||
		strings.Contains(logContent, "driver: bad connection") ||
		strings.Contains(logContent, "connection.go") ||
		strings.Contains(logContent, "packets.go") {

		// 使用我们的日志系统记录为错误级别，但不输出到控制台
		if zlog.Sugar != nil {
			zlog.Error("MySQL驱动连接错误:", strings.TrimSpace(logContent))
		}
	}

	// 将原始日志写入MySQL驱动专用日志文件
	return w.logFile.Write(p)
}

func InitConn(mysqlbase MysqlBase) (db *sql.DB, err error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&timeout=5s",
		mysqlbase.Username,
		mysqlbase.Pwd,
		mysqlbase.Ipaddr,
		mysqlbase.Port,
		mysqlbase.Database,
	)
	db, err = sql.Open("mysql", dsn)
	if err != nil {
		Re.Status = ConnDatabase
		zlog.Error("连接数据库失败")
		return nil, err
	}
	return db, nil
}

// 同步远程表结构到内存中
func (conn *Conn) RemoteSyncShame() {
	// 获取远程表结构

	// 创建替换表名创建临时表
	// 对比当前表
	if Backinfo == nil {
		Re.Status = ReadConfig
		zlog.Error("获取配置失败")
		os.Exit(1)
	}
	for tableau := range Backinfo {
		zlog.Debug(conn.Tableschme(tableau))
	}
}

func (conn *Conn) ReadSql(sqls string) ([]map[string]string, error) {
	zlog.Debug("进入ReadSql方法，SQL长度:", len(sqls))
	zlog.Debug("执行SQL:", sqls)

	rows, err := conn.Db.Query(sqls)
	if err != nil {
		Re.Status = ConnDatabase
		zlog.Error("执行SQL查询失败，SQL:", sqls, "错误:", err.Error())
		return nil, err
	} else {
		Re.Status = 0
		zlog.Debug("SQL查询执行成功")
	}
	defer func() {
		if rows != nil {
			rows.Close()
			zlog.Debug("数据库查询结果集已关闭")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		Re.Status = ReadRaw
		zlog.Error("获取查询结果列信息失败，SQL:", sqls)
		zlog.Error("【AlterColumn】失败原因:", err.Error())
		return nil, err
	} else {
		Re.Status = 0
		zlog.Debug("获取查询结果列信息成功，列数:", len(columns))
	}

	var manages []Manage
	rowCount := 0

	// 扫描每一行信息
	zlog.Debug("开始扫描查询结果")
	for rows.Next() {
		rowCount++
		keys := make([]string, 0, len(columns))
		resultmap := make(map[string]string, len(columns))
		// 每一行数据长度
		rowdatas := make([]interface{}, len(columns))
		for i := 0; i < len(rowdatas); i++ {
			// 每一行的数据带是否为空的标志位
			rowdatas[i] = makeEmptyStr()
		}

		// 查询一行数据绑定到rowdatas
		err := rows.Scan(rowdatas...)
		if err != nil {
			Re.Status = ReadRaw
			zlog.Error("扫描第", rowCount, "行数据失败，错误:", err.Error())
			return nil, err
		}

		for i := 0; i < len(rowdatas); i++ {
			k := columns[i]
			val := rowdatas[i].(*sql.NullString)
			v := ""
			if val.Valid {
				v = val.String
			}
			resultmap[k] = v
			keys = append(keys, k)
		}
		// keys 列名
		manages = append(manages, Manage{base: resultmap, keys: keys})

	}
	zlog.Debug("数据扫描完成，总行数:", rowCount)

	var result []map[string]string
	zlog.Debug("开始构建最终结果集")
	for i, manage := range manages {
		rowdata := make(map[string]string)
		for j := 0; j < len(manage.keys); j++ {
			k := manage.keys[j]
			v := manage.base[k]
			if k != "" {
				rowdata[k] = v
			}
		}
		result = append(result, rowdata)
		if i < 3 { // 只打印前3行的详细信息，避免日志过多
			zlog.Debug("第", i+1, "行数据:", rowdata)
		}
	}
	zlog.Debug("ReadSql方法执行完成，返回结果行数:", len(result))
	return result, nil
}

func (conn *Conn) TableExist(tablename string) bool {
	zlog.Debug("进入TableExist方法，检查表是否存在:", tablename)
	var (
		xx int
	)
	querySQL := "select 1 as xx from INFORMATION_SCHEMA.STATISTICS where TABLE_NAME = ?"
	zlog.Debug("执行表存在性检查SQL:", querySQL, "参数:", tablename)
	err := conn.Db.QueryRow(querySQL, tablename).Scan(&xx)
	if err != nil {
		if err == sql.ErrNoRows {
			zlog.Debug("表不存在（无查询结果）:", tablename)
			return false
		}
		Re.Status = TableNotExsit
		zlog.Error("检查表存在性时发生错误，表名:", tablename, "错误:", err.Error())
		return false
	}
	if xx == 1 {
		zlog.Debug("表存在检查通过，表名:", tablename)
		return true
	} else {
		zlog.Debug("表不存在，表名:", tablename)
		return false
	}
}

// Createtable 创建表方法
func (conn *Conn) Createtable(sql string) bool {
	zlog.Info("【Createtable】==================== 开始创建表 ====================")
	zlog.Debug("【Createtable】步骤1: 进入Createtable方法")

	if len(sql) == 0 {
		zlog.Error("【Createtable】步骤2: 创建表失败 ✗")
		zlog.Error("【Createtable】失败原因: SQL语句为空")
		Re.Status = CreateTbale
		zlog.Error("【Createtable】==================== 创建表失败 ====================")
		return false
	}

	zlog.Debug("【Createtable】步骤2: 验证SQL语句")
	zlog.Debug("【Createtable】SQL长度:", len(sql), "字节")

	// 显示SQL预览
	if len(sql) > 200 {
		zlog.Debug("【Createtable】SQL预览:", sql[:200], "...")
	} else {
		zlog.Debug("【Createtable】完整SQL:", sql)
	}

	zlog.Debug("【Createtable】步骤3: 执行创建表SQL")
	if _, err := conn.Db.Exec(sql); err != nil {
		zlog.Error("【Createtable】步骤3: 创建表失败 ✗")
		zlog.Error("【Createtable】失败SQL:", sql)
		zlog.Error("【Createtable】失败原因:", err.Error())
		Re.Status = CreateTbale
		zlog.Error("【Createtable】==================== 创建表失败 ====================")
		return false
	}

	zlog.Info("【Createtable】步骤3: SQL执行完成 ✓")

	// 步骤4: 验证表是否真的创建成功
	tableName := conn.extractTableNameFromDDL(sql)
	if tableName == "" {
		zlog.Error("【Createtable】步骤4: 无法从SQL中提取表名")
		zlog.Error("【Createtable】==================== 创建表验证失败 ====================")
		return false
	}

	zlog.Info("【Createtable】步骤4: 开始验证表是否真的创建成功，表名:", tableName)
	if !conn.TableExist(tableName) {
		zlog.Error("【Createtable】步骤4: 验证失败，表不存在:", tableName)
		zlog.Error("【Createtable】==================== 创建表验证失败 ====================")
		return false
	}

	// 步骤5: 验证表结构是否正确
	zlog.Info("【Createtable】步骤5: 开始验证表结构是否正确")
	if !conn.verifyTableStructure(tableName, sql) {
		zlog.Error("【Createtable】步骤5: 验证失败，表结构不正确:", tableName)
		zlog.Error("【Createtable】==================== 创建表验证失败 ====================")
		return false
	}

	zlog.Info("【Createtable】步骤5: 表结构验证成功 ✓")
	zlog.Info("【Createtable】==================== 创建表成功 ====================")
	return true
}

var (
	ok         bool
	ActionNum  int64
	colume     string
	columetype string
)

// 删除已存在的表
func (conn *Conn) DropTable(TableName string) bool {
	zlog.Debug("进入DropTable方法，表名:", TableName)
	dropSQL := fmt.Sprintf("drop table if exists %s", TableName)
	zlog.Debug("执行删除表SQL:", dropSQL)
	if _, err := conn.Db.Exec(dropSQL); err != nil {
		zlog.Error("删除表失败，表名:", TableName, "错误:", err.Error())
		return false
	}
	zlog.Debug("删除表成功，表名:", TableName)
	return true
}

// 删除create table中末尾的逗号
func (conn *Conn) RemoveComma(RepairStr string) string {
	if string(RepairStr[len(RepairStr)-1]) == "," {
		return RepairStr[:len(RepairStr)-1]
	}
	return ""
}

// 插入数据
func (conn *Conn) SqlAction(sql string) bool {
	zlog.Debug("【SqlAction】开始执行SQL，长度:", len(sql), "字节")

	// 显示SQL的关键信息
	if len(sql) > 0 {
		sqlPreview := sql
		if len(sqlPreview) > 200 {
			sqlPreview = sqlPreview[:200] + "..."
		}
		zlog.Debug("【SqlAction】SQL预览:", sqlPreview)
	}

	_, err := conn.Db.Exec(sql)
	if err != nil {
		zlog.Error("【SqlAction】SQL执行失败")
		zlog.Error("【SqlAction】错误信息:", err.Error())
		zlog.Error("【SqlAction】SQL长度:", len(sql))

		// 分析错误类型
		errorMsg := err.Error()
		if strings.Contains(errorMsg, "syntax error") {
			zlog.Error("【SqlAction】错误类型: SQL语法错误")
		} else if strings.Contains(errorMsg, "connection") {
			zlog.Error("【SqlAction】错误类型: 数据库连接问题")
		} else if strings.Contains(errorMsg, "permission") || strings.Contains(errorMsg, "access denied") {
			zlog.Error("【SqlAction】错误类型: 权限不足")
		} else if strings.Contains(errorMsg, "duplicate") || strings.Contains(errorMsg, "already exists") {
			zlog.Error("【SqlAction】错误类型: 重复或已存在")
		} else {
			zlog.Error("【SqlAction】错误类型: 其他数据库错误")
		}

		// 显示完整SQL用于调试（如果不太长）
		if len(sql) <= 1000 {
			zlog.Error("【SqlAction】完整SQL:", sql)
		} else {
			zlog.Error("【SqlAction】SQL前500字符:", sql[:500])
			zlog.Error("【SqlAction】SQL后500字符:", sql[len(sql)-500:])
		}

		return false
	}

	zlog.Debug("【SqlAction】SQL执行成功")
	return true
}

// ActionSql 获取动作sql
func (conn *Conn) ActionSql(DestTable string, AlterColumns map[string]any) []string {
	zlog.Debug("【ActionSql】==================== 开始生成ALTER SQL ====================")
	zlog.Debug("【ActionSql】步骤1: 进入ActionSql方法")
	zlog.Debug("【ActionSql】目标表:", DestTable)
	zlog.Debug("【ActionSql】需要修改的字段:", AlterColumns)

	var ActionSqls []string

	// 所有表名
	zlog.Debug("【ActionSql】步骤2: 获取所有缓存的表名")
	allValues := utils.Keys_(&Role)
	zlog.Debug("【ActionSql】缓存中的所有键列表:", allValues)
	zlog.Debug("【ActionSql】目标表名:", DestTable)

	// 分析缓存中的键类型
	var tableKeys []string
	var verifiedKeys []string
	for _, key := range allValues {
		if strings.HasSuffix(key, "_verified") {
			verifiedKeys = append(verifiedKeys, key)
		} else {
			tableKeys = append(tableKeys, key)
		}
	}
	zlog.Debug("【ActionSql】表结构键列表:", tableKeys)
	zlog.Debug("【ActionSql】验证标记键列表:", verifiedKeys)

	// 找出相似度最高的表名
	zlog.Debug("【ActionSql】步骤3: 查找最相似的表名")
	zlog.Debug("【ActionSql】在所有键中查找:", allValues)
	key_ := utils.FindMostSimilarValue(DestTable, allValues)
	zlog.Debug("【ActionSql】最相似的表名:", key_)

	// 检查匹配结果的类型
	if strings.HasSuffix(key_, "_verified") {
		zlog.Error("【ActionSql】警告: 匹配到了验证标记键!")
		zlog.Error("【ActionSql】匹配结果:", key_)
		zlog.Error("【ActionSql】目标表名:", DestTable)

		// 尝试在表结构键中重新查找
		if len(tableKeys) > 0 {
			zlog.Debug("【ActionSql】尝试在表结构键中重新查找...")
			key_ = utils.FindMostSimilarValue(DestTable, tableKeys)
			zlog.Debug("【ActionSql】重新匹配的表名:", key_)
		} else {
			zlog.Error("【ActionSql】没有可用的表结构键，无法生成ALTER SQL")
			return nil
		}
	}

	// 验证匹配的键是否合理
	if key_ == "" {
		zlog.Error("【ActionSql】未找到匹配的表结构键")
		zlog.Error("【ActionSql】目标表名:", DestTable)
		zlog.Error("【ActionSql】所有可用键:", allValues)
		return nil
	}

	if body, ok := Role.Load(key_); ok {
		zlog.Debug("【ActionSql】步骤4: 从缓存加载数据成功 ✓")
		zlog.Debug("【ActionSql】加载的键:", key_)
		zlog.Debug("【ActionSql】数据类型:", fmt.Sprintf("%T", body))

		// 检查类型，确保是[]byte类型的表结构数据，而不是string类型的验证标记
		if bodyBytes, isByteSlice := body.([]byte); isByteSlice {
			zlog.Debug("【ActionSql】✓ 确认为表结构数据（[]byte类型）")
			zlog.Debug("【ActionSql】表结构长度:", len(bodyBytes), "字节")
			createTableSQL := string(bodyBytes)
			newbody := strings.Split(createTableSQL, "\n")
			zlog.Debug("【ActionSql】表结构行数:", len(newbody))

			// 解析字段顺序
			zlog.Debug("【ActionSql】步骤4.1: 解析表结构中的字段顺序")
			fieldOrder := parseFieldOrder(createTableSQL)

			zlog.Debug("【ActionSql】步骤5: 开始生成ALTER SQL")
			addCount := 0
			changeCount := 0

			for lineNum, i := range newbody {
				for ColumeName, ActionNum := range AlterColumns {
					// 包含列名且不包含索引名
					if strings.Contains(i, fmt.Sprintf("`%s`", ColumeName)) && !strings.Contains(i, "KEY") {
						zlog.Debug("【ActionSql】步骤5.", lineNum+1, ": 找到字段定义")
						zlog.Debug("【ActionSql】字段名:", ColumeName)
						zlog.Debug("【ActionSql】操作类型:", ActionNum)
						zlog.Debug("【ActionSql】字段定义行:", i)

						switch ActionNum.(int64) {
						// 增加字段
						case 1:
							// 查找字段应该插入的位置
							position, _ := findFieldPosition(ColumeName, fieldOrder)

							// 生成带位置信息的ADD SQL
							fieldDef := conn.RemoveComma(i)
							// 验证表名格式，避免生成错误的SQL
							if strings.Contains(DestTable, "__") {
								zlog.Error("【ActionSql】检测到表名中有双下划线，可能存在错误:", DestTable)
								// 尝试修复表名
								fixedTableName := strings.ReplaceAll(DestTable, "__", "_")
								zlog.Debug("【ActionSql】尝试修复表名:", fixedTableName)
								DestTable = fixedTableName
							}
							sql := fmt.Sprintf("ALTER TABLE %s ADD %s %s", DestTable, fieldDef, position)

							ActionSqls = append(ActionSqls, sql)
							addCount++
							zlog.Debug("【ActionSql】生成ADD SQL ✓:", sql)
							zlog.Debug("【ActionSql】字段位置:", position)
						// 修改字段
						case 2:
							// 验证表名格式，避免生成错误的SQL
							if strings.Contains(DestTable, "__") {
								zlog.Error("【ActionSql】检测到表名中有双下划线，可能存在错误:", DestTable)
								// 尝试修复表名
								fixedTableName := strings.ReplaceAll(DestTable, "__", "_")
								zlog.Debug("【ActionSql】尝试修复表名:", fixedTableName)
								DestTable = fixedTableName
							}
							sql := conn.RemoveComma(fmt.Sprintf("ALTER TABLE %s CHANGE %s %s", DestTable, ColumeName, i))
							ActionSqls = append(ActionSqls, sql)
							changeCount++
							zlog.Debug("【ActionSql】生成CHANGE SQL ✓:", sql)
						default:
							zlog.Warn("【ActionSql】未知操作类型:", ActionNum)
						}
					}
				}
			}

			zlog.Debug("【ActionSql】步骤5: SQL生成完成 ✓")
			zlog.Info("【ActionSql】统计: 生成", addCount, "个ADD SQL,", changeCount, "个CHANGE SQL")
			zlog.Debug("【ActionSql】总计生成:", len(ActionSqls), "个SQL")
			zlog.Info("【ActionSql】==================== ALTER SQL生成成功 ====================")
			return ActionSqls
		} else {
			// 缓存中的值不是[]byte类型（可能是验证标记等字符串类型）
			zlog.Error("【ActionSql】步骤4: 缓存中的值类型错误 ✗")
			zlog.Error("【ActionSql】失败原因: 键", key_, "对应的值不是表结构数据（[]byte类型）")
			zlog.Error("【ActionSql】实际类型:", fmt.Sprintf("%T", body))
			zlog.Error("【ActionSql】实际值:", body)
			zlog.Error("【ActionSql】目标表名:", DestTable)
			zlog.Error("【ActionSql】匹配到的键:", key_)

			// 如果是验证标记，尝试查找对应的表结构键
			if strings.HasSuffix(key_, "_verified") {
				zlog.Error("【ActionSql】这是一个验证标记键，尝试查找对应的表结构键...")
				baseTableName := strings.TrimSuffix(key_, "_verified")
				zlog.Error("【ActionSql】推测的表结构键:", baseTableName)

				if baseBody, exists := Role.Load(baseTableName); exists {
					zlog.Error("【ActionSql】找到对应的表结构键:", baseTableName)
					zlog.Error("【ActionSql】表结构数据类型:", fmt.Sprintf("%T", baseBody))
				} else {
					zlog.Error("【ActionSql】未找到对应的表结构键:", baseTableName)
				}
			}
		}
	} else {
		zlog.Error("【ActionSql】步骤4: 从缓存加载数据失败 ✗")
		zlog.Error("【ActionSql】失败原因: 键", key_, "不存在于缓存中")
		zlog.Error("【ActionSql】目标表名:", DestTable)
		zlog.Error("【ActionSql】匹配到的键:", key_)
		zlog.Error("【ActionSql】所有可用键:", allValues)
		zlog.Error("【ActionSql】表结构键:", tableKeys)
		zlog.Error("【ActionSql】验证标记键:", verifiedKeys)
		zlog.Error("【ActionSql】==================== ALTER SQL生成失败 ====================")
	}
	return nil
}

// 获取表的所有列
func (conn *Conn) GetColumnNames(tableName, dbName string) ([]string, error) {
	query := fmt.Sprintf("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '%s' and TABLE_SCHEMA = '%s'", tableName, dbName)
	rows, err := conn.Db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var columns []string
	for rows.Next() {
		var column string
		err := rows.Scan(&column)
		if err != nil {
			return nil, err
		}
		columns = append(columns, column)
	}

	return columns, nil
}

// AlterColumn 修改表字段
func (conn *Conn) AlterColumn(SrcName string, DestTable string) bool {
	zlog.Info("【AlterColumn】==================== 开始表结构差异分析 ====================")
	zlog.Debug("【AlterColumn】步骤1: 进入AlterColumn方法")
	zlog.Debug("【AlterColumn】源表:", SrcName)
	zlog.Debug("【AlterColumn】目标表:", DestTable)

	// 步骤1.1: 检查源表是否存在
	if !conn.TableExist(SrcName) {
		zlog.Error("【AlterColumn】步骤1.1: 源表不存在 ✗")
		zlog.Error("【AlterColumn】源表名:", SrcName)
		zlog.Error("【AlterColumn】==================== 表结构差异分析失败 ====================")
		return false
	}
	zlog.Debug("【AlterColumn】步骤1.1: 源表存在性检查通过 ✓")

	// 步骤1.2: 检查目标表是否存在
	if !conn.TableExist(DestTable) {
		zlog.Error("【AlterColumn】步骤1.2: 目标表不存在 ✗")
		zlog.Error("【AlterColumn】目标表名:", DestTable)
		zlog.Error("【AlterColumn】==================== 表结构差异分析失败 ====================")
		return false
	}
	zlog.Debug("【AlterColumn】步骤1.2: 目标表存在性检查通过 ✓")

	AlterColumns := map[string]any{}
	zlog.Debug("【AlterColumn】步骤2: 初始化差异分析容器 ✓")

	// 1 找出不一样的字段
	zlog.Debug("【AlterColumn】步骤3: 构建表结构差异查询SQL")
	sqlstr := fmt.Sprintf(`
        select
            t1_COLUMN_NAME,
            t1_COLUMN_TYPE,
            t2_COLUMN_NAME,
            t2_COLUMN_TYPE
        from
            (
            select
                COLUMN_NAME as t1_COLUMN_NAME,
                COLUMN_TYPE as t1_COLUMN_TYPE
            from
                information_schema.COLUMNS
            where
                table_schema = 'gstation'
                and table_name = '%s') t1
        left join
        (
            select
                COLUMN_NAME as t2_COLUMN_NAME,
                COLUMN_TYPE as t2_COLUMN_TYPE
            from
                information_schema.COLUMNS
            where
                table_schema = 'gstation'
                and table_name = '%s') t2
        on
            t1_COLUMN_NAME = t2_COLUMN_NAME
        where
        	t1_COLUMN_TYPE != t2_COLUMN_TYPE
        	or t2_COLUMN_NAME is null
        	or t2_COLUMN_TYPE is null
`, SrcName, DestTable)
	zlog.Debug("【AlterColumn】步骤3: SQL构建完成 ✓")
	zlog.Debug("【AlterColumn】查询参数: 源表=", SrcName, ", 目标表=", DestTable)
	if len(sqlstr) > 500 {
		zlog.Debug("【AlterColumn】SQL预览:", sqlstr[:500], "...")
	} else {
		zlog.Debug("【AlterColumn】完整SQL:", sqlstr)
	}
	zlog.Debug("【AlterColumn】步骤4: 执行表结构差异查询")
	diffdf, err := conn.ReadSql(sqlstr)
	if err != nil {
		zlog.Error("【AlterColumn】步骤4: 查询表结构差异失败 ✗")
		zlog.Error("【AlterColumn】失败原因:", err.Error())
		zlog.Error("【AlterColumn】查询参数: 源表=", SrcName, ", 目标表=", DestTable)
		zlog.Error("【AlterColumn】==================== 表结构差异分析失败 ====================")
		return false
	}
	zlog.Debug("【AlterColumn】步骤4: 查询表结构差异成功 ✓")
	zlog.Debug("【AlterColumn】差异记录数量:", len(diffdf))

	if len(diffdf) > 0 {
		zlog.Debug("【AlterColumn】差异详情:")
		for i, v := range diffdf {
			zlog.Debug("【AlterColumn】  差异", i+1, ":", v)
		}
	}

	// 遍历出字段名或者类型不同的字段信息
	zlog.Debug("【AlterColumn】步骤5: 分析表结构差异")
	addCount := 0
	changeCount := 0

	for i, v := range diffdf {
		zlog.Debug("【AlterColumn】步骤5.", i+1, ": 处理差异记录")
		zlog.Debug("【AlterColumn】差异内容:", v)

		if colume, ok = v["t2_COLUMN_NAME"]; ok {
			if columetype, ok = v["t2_COLUMN_TYPE"]; ok {
				if len(columetype) < 1 {
					// ADD
					ActionNum = 1
					addCount++
					zlog.Debug("【AlterColumn】检测到需要添加字段 ✓")
					zlog.Debug("【AlterColumn】  - 字段名:", v["t1_COLUMN_NAME"])
					zlog.Debug("【AlterColumn】  - 字段类型:", v["t1_COLUMN_TYPE"])
					zlog.Debug("【AlterColumn】  - 操作类型: ADD")
				}
				if len(columetype) >= 1 {
					// CHANGE
					ActionNum = 2
					changeCount++
					zlog.Debug("【AlterColumn】检测到需要修改字段 ✓")
					zlog.Debug("【AlterColumn】  - 字段名:", v["t1_COLUMN_NAME"])
					zlog.Debug("【AlterColumn】  - 原类型:", columetype)
					zlog.Debug("【AlterColumn】  - 新类型:", v["t1_COLUMN_TYPE"])
					zlog.Debug("【AlterColumn】  - 操作类型: CHANGE")
				}
			}
			AlterColumns[v["t1_COLUMN_NAME"]] = ActionNum
		}
	}

	zlog.Debug("【AlterColumn】步骤5: 差异分析完成 ✓")
	zlog.Info("【AlterColumn】统计结果: 需要添加", addCount, "个字段, 需要修改", changeCount, "个字段")

	if len(AlterColumns) == 0 {
		zlog.Info("【AlterColumn】步骤6: 未发现表结构差异 ✓")
		zlog.Debug("【AlterColumn】结论: 表结构完全一致，无需修改")
		zlog.Info("【AlterColumn】==================== 表结构差异分析完成(无差异) ====================")
		return true
	}

	zlog.Info("【AlterColumn】步骤6: 发现表结构差异")
	zlog.Debug("【AlterColumn】需要修改的字段:", AlterColumns)

	// 5 执行sql
	zlog.Debug("【AlterColumn】步骤7: 生成表结构修改SQL")
	actionSqls := conn.ActionSql(DestTable, AlterColumns)
	zlog.Debug("【AlterColumn】步骤7: SQL生成完成 ✓")
	zlog.Debug("【AlterColumn】生成的修改SQL数量:", len(actionSqls))

	if len(actionSqls) > 0 {
		zlog.Debug("【AlterColumn】生成的SQL列表:")
		for i, sql := range actionSqls {
			zlog.Debug("【AlterColumn】  SQL", i+1, ":", sql)
		}
	}

	zlog.Debug("【AlterColumn】步骤8: 开始执行表结构修改SQL")
	successCount := 0
	for i, Sqlstr := range actionSqls {
		zlog.Debug("【AlterColumn】步骤8.", i+1, ": 执行第", i+1, "个SQL")
		zlog.Debug("【AlterColumn】SQL内容:", Sqlstr)

		if _, err := conn.Db.Exec(Sqlstr); err != nil {
			zlog.Error("【AlterColumn】步骤8.", i+1, ": SQL执行失败 ✗")
			zlog.Error("【AlterColumn】失败的SQL:", Sqlstr)
			zlog.Error("【AlterColumn】失败原因:", err.Error())
			zlog.Error("【AlterColumn】已成功执行:", successCount, "个SQL")
			zlog.Error("【AlterColumn】==================== 表结构差异分析失败 ====================")
			return false
		}
		zlog.Debug("【AlterColumn】步骤8.", i+1, ": SQL执行成功 ✓")
		successCount++
	}

	zlog.Debug("【AlterColumn】步骤8: 所有SQL执行完成 ✓")
	zlog.Info("【AlterColumn】成功执行:", successCount, "个SQL")

	// 步骤9: 验证表结构同步是否真的成功
	zlog.Info("【AlterColumn】步骤9: 开始验证表结构同步结果")
	if !conn.verifyTableSyncResult(SrcName, DestTable) {
		zlog.Error("【AlterColumn】步骤9: 表结构同步验证失败 ✗")
		zlog.Error("【AlterColumn】源表:", SrcName, "目标表:", DestTable)
		zlog.Error("【AlterColumn】==================== 表结构差异分析失败 ====================")
		return false
	}
	zlog.Info("【AlterColumn】步骤9: 表结构同步验证成功 ✓")

	zlog.Debug("【AlterColumn】步骤10: AlterColumn方法执行成功")
	zlog.Info("【AlterColumn】同步结果: ", SrcName, "->", DestTable, "完成")
	zlog.Info("【AlterColumn】==================== 表结构差异分析成功 ====================")
	return true
}

// AnalyzeTable 分析表
func (conn *Conn) AnalyzeTable(TableName string) {
	//// 分析Cardinality 与 真实数据之差
	//`SHOW INDEX FROM gantry_jour`
	//// 如果当天有更新数据就执行一次 analyze table
	//sqlstr := fmt.Sprintf(`select
	//    1
	//from
	//    information_schema.tables
	//where
	//    table_schema = 'gstation'
	//    and table_name = '%s'
	//    and UPDATE_TIME < date_format(now(),
	//    '%%Y-%%m-%%d 00:00:00')`,TableName)
	zlog.Debug(fmt.Sprintf("更新 %s 表统计信息", TableName))
	_, err := conn.Db.Exec(fmt.Sprintf(`analyze table %s`, TableName))
	if err != nil {
		Re.Status = AnalyzeTable
		zlog.Error(fmt.Sprintf("analyze %s 表失败:", TableName), err.Error())
	}
}

// 如果目标表不存在就克隆当前表
func (conn *Conn) CloningTable(srcTable, desTable string) {
	zlog.Info("【克隆表】开始克隆表，源表:", srcTable, "目标表:", desTable)

	// 检查源表是否存在
	if !conn.TableExist(srcTable) {
		zlog.Error("【克隆表】源表不存在:", srcTable)
		Re.Status = CloneTable
		return
	}

	// 执行克隆操作
	cloneSQL := fmt.Sprintf("create table if not exists %s like %s", desTable, srcTable)
	zlog.Debug("【克隆表】执行克隆SQL:", cloneSQL)

	if _, err := conn.Db.Exec(cloneSQL); err != nil {
		Re.Status = CloneTable
		zlog.Error("【克隆表】克隆表失败，SQL:", cloneSQL, "错误:", err.Error())
		return
	}

	// 验证克隆是否成功
	zlog.Info("【克隆表】SQL执行完成，开始验证克隆结果")
	if !conn.TableExist(desTable) {
		zlog.Error("【克隆表】验证失败：目标表不存在:", desTable)
		Re.Status = CloneTable
		return
	}

	// 验证表结构是否一致
	if !conn.verifyTableSyncResult(srcTable, desTable) {
		zlog.Error("【克隆表】验证失败：表结构不一致")
		Re.Status = CloneTable
		return
	}

	zlog.Info("【克隆表】克隆表成功并验证通过，源表:", srcTable, "目标表:", desTable)
}

// 同步当天表结构
func (conn *Conn) SyncTable(tableau string) {
	zlog.Info("【SyncTable】==================== 开始表结构同步 ====================")
	zlog.Debug("【SyncTable】步骤1: 进入SyncTable方法")
	zlog.Debug("【SyncTable】目标表名:", tableau)

	// 强制刷新表结构配置缓存
	zlog.Debug("【SyncTable】步骤2: 准备刷新表结构配置缓存")
	schemaURL := fmt.Sprintf("%s/nacosGetConfig/ContextPath/service/tableschme?tablename=%s", NacosUrl, tableau)
	zlog.Debug("【SyncTable】步骤2: 构建表结构请求URL ✓")
	zlog.Debug("【SyncTable】请求URL:", schemaURL)

	// 检查缓存中是否已有旧的表结构
	zlog.Debug("【SyncTable】步骤3: 检查缓存中的旧表结构")
	var oldSchema []byte
	if oldBody, exists := Role.Load(tableau); exists {
		oldSchema = oldBody.([]byte)
		zlog.Debug("【SyncTable】步骤3: 发现缓存中的旧表结构 ✓")
		zlog.Debug("【SyncTable】旧结构长度:", len(oldSchema), "字节")

		// 显示旧结构的完整内容用于调试
		oldStr := string(oldSchema)
		if strings.Contains(oldStr, "CREATE TABLE") {
			zlog.Debug("【SyncTable】旧结构类型: CREATE TABLE语句")
		}
		zlog.Debug("【SyncTable】缓存中的完整旧结构:")
		zlog.Debug(oldStr)
	} else {
		zlog.Debug("【SyncTable】步骤3: 缓存中无此表结构")
		zlog.Debug("【SyncTable】这是首次获取该表结构")
	}

	// 获取最新的表结构配置
	zlog.Debug("【SyncTable】步骤4: 开始获取最新表结构配置")
	body := GetConfig(schemaURL)
	if body != nil {
		zlog.Debug("【SyncTable】步骤4: 获取最新表结构成功 ✓")
		zlog.Debug("【SyncTable】新结构长度:", len(body), "字节")

		// 比较新旧结构是否有变化（使用标准化对比提高准确性）
		zlog.Debug("【SyncTable】步骤5: 比较新旧表结构配置")

		var isConfigChanged bool
		if oldSchema != nil {
			zlog.Debug("【对比调试】==================== 开始详细对比分析 ====================")
			zlog.Debug("【对比调试】原始旧结构长度:", len(oldSchema), "字节")
			zlog.Debug("【对比调试】原始新结构长度:", len(body), "字节")

			// 显示原始内容
			oldStr := string(oldSchema)
			newStr := string(body)
			zlog.Debug("【对比调试】原始旧结构内容:")
			zlog.Debug(oldStr)
			zlog.Debug("【对比调试】原始新结构内容:")
			zlog.Debug(newStr)

			// 原始字符串对比
			rawEqual := oldStr == newStr
			zlog.Debug("【对比调试】原始字符串对比结果:", rawEqual)

			// 使用标准化对比，避免格式差异导致的误判
			zlog.Debug("【对比调试】开始标准化处理...")
			oldNormalized := normalizeSchema(oldStr)
			newNormalized := normalizeSchema(newStr)

			zlog.Debug("【对比调试】标准化后旧结构长度:", len(oldNormalized), "字节")
			zlog.Debug("【对比调试】标准化后新结构长度:", len(newNormalized), "字节")
			zlog.Debug("【对比调试】标准化后旧结构内容:")
			zlog.Debug(oldNormalized)
			zlog.Debug("【对比调试】标准化后新结构内容:")
			zlog.Debug(newNormalized)

			isConfigChanged = oldNormalized != newNormalized
			zlog.Debug("【对比调试】标准化字符串对比结果:", !isConfigChanged, "(true=相同, false=不同)")

			// 如果标准化后仍然相同，进行逐字符对比找出差异
			if !isConfigChanged {
				zlog.Debug("【对比调试】标准化后判断相同，进行详细验证...")
				if len(oldNormalized) != len(newNormalized) {
					zlog.Debug("【对比调试】长度不同！旧:", len(oldNormalized), "新:", len(newNormalized))
				} else {
					zlog.Debug("【对比调试】长度相同，进行逐字符对比...")
					for i := 0; i < len(oldNormalized); i++ {
						if oldNormalized[i] != newNormalized[i] {
							zlog.Debug("【对比调试】发现差异位置:", i, "旧字符:", string(oldNormalized[i]), "新字符:", string(newNormalized[i]))
							break
						}
					}
				}
			} else {
				zlog.Debug("【对比调试】标准化后发现差异！")
			}
			zlog.Debug("【对比调试】==================== 对比分析完成 ====================")

			zlog.Debug("【SyncTable】步骤5.1: 标准化对比完成")
			if !isConfigChanged {
				zlog.Info("【SyncTable】步骤5: 表结构配置无变化 ✓")
				zlog.Debug("【SyncTable】标准化后结构相同，长度:", len(newNormalized), "字节")

				// 输出详细的对比信息用于调试
				zlog.Debug("【SyncTable】旧结构标准化内容:")
				zlog.Debug(oldNormalized)
				zlog.Debug("【SyncTable】新结构标准化内容:")
				zlog.Debug(newNormalized)
				zlog.Debug("【SyncTable】对比结果: 完全相同")

				// 🎯 配置无变化时，检查是否需要验证数据库实际结构
				verifiedKey := tableau + "_verified"
				if _, exists := Role.Load(verifiedKey); exists {
					// 已经验证过，但需要快速检查数据库表是否还存在且结构正确
					zlog.Info("【SyncTable】配置无变化且已验证过，进行快速结构检查")

					if !conn.TableExist(tableau) {
						zlog.Warn("【SyncTable】目标表不存在！清除验证标记，需要重新同步")
						Role.Delete(verifiedKey)
					} else {
						// 快速检查：对比原始表和配置中的字段数量
						if conn.quickStructureCheck(tableau, string(body)) {
							zlog.Info("【SyncTable】快速检查通过，跳过同步")
							SetSyncStatus(tableau, SyncSuccessNoChanges, GetSyncStatusMessage(SyncSuccessNoChanges))
							zlog.Info("【SyncTable】==================== 表结构同步完成(无需变化) ====================")
							return
						} else {
							zlog.Warn("【SyncTable】快速检查失败，清除验证标记，需要重新同步")
							Role.Delete(verifiedKey)
						}
					}
				} else {
					zlog.Info("【SyncTable】配置无变化但未验证过，需要验证数据库实际结构")
				}
			} else {
				zlog.Debug("【SyncTable】标准化后结构不同 - 旧长度:", len(oldNormalized), "新长度:", len(newNormalized))

				// 输出详细的差异信息用于调试
				zlog.Debug("【SyncTable】旧结构标准化内容:")
				zlog.Debug(oldNormalized)
				zlog.Debug("【SyncTable】新结构标准化内容:")
				zlog.Debug(newNormalized)
			}
		} else {
			// 首次获取表结构，肯定需要处理
			isConfigChanged = true
			zlog.Debug("【SyncTable】步骤5.1: 首次获取表结构配置")
		}

		if isConfigChanged {
			zlog.Info("【SyncTable】步骤5: 检测到表结构配置变化！✓")

			// 🎯 清除验证标记，因为配置已变化，需要重新验证
			verifiedKey := tableau + "_verified"
			Role.Delete(verifiedKey)
			zlog.Debug("【SyncTable】清除验证标记:", verifiedKey, "因为配置已变化")

			if oldSchema != nil {
				zlog.Debug("【SyncTable】配置对比 - 旧长度:", len(oldSchema), "新长度:", len(body))
				zlog.Debug("【SyncTable】变化类型: 表结构配置更新")
			} else {
				zlog.Debug("【SyncTable】变化类型: 首次获取表结构配置")
			}

			// 显示新结构的关键信息
			newStr := string(body)
			if strings.Contains(newStr, "CREATE TABLE") {
				zlog.Debug("【SyncTable】新配置类型: CREATE TABLE语句")
			}
			if len(newStr) > 100 {
				zlog.Debug("【SyncTable】新配置预览:", newStr[:100], "...")
			}
		}

		// 更新缓存
		zlog.Debug("【SyncTable】步骤6: 更新表结构缓存")
		Role.Store(tableau, body)
		zlog.Debug("【SyncTable】步骤6: 表结构缓存更新成功 ✓")
		zlog.Debug("【SyncTable】缓存的结构长度:", len(body), "字节")
	} else {
		zlog.Error("【SyncTable】步骤4: 获取最新表结构失败 ✗")
		zlog.Error("【SyncTable】失败URL:", schemaURL)

		if oldSchema != nil {
			zlog.Warn("【SyncTable】检测到缓存中有旧结构，长度:", len(oldSchema))
			zlog.Info("【SyncTable】由于无法获取最新配置，跳过同步操作")

			// 设置同步状态为获取表结构失败，但有缓存可用
			SetSyncStatus(tableau, SyncFailGetSchema, GetSyncStatusMessage(SyncFailGetSchema))
			zlog.Warn("【SyncTable】设置同步状态: 网络失败但有缓存")
			zlog.Warn("【SyncTable】==================== 表结构同步跳过(网络失败) ====================")
			return
		} else {
			zlog.Error("【SyncTable】严重错误: 既无法获取新结构，也无缓存结构")
			zlog.Error("【SyncTable】无法进行任何表结构操作")

			// 设置同步状态为配置缺失
			SetSyncStatus(tableau, SyncFailConfigMissing, GetSyncStatusMessage(SyncFailConfigMissing))
			zlog.Error("【SyncTable】设置同步状态: 无配置可用")
			zlog.Error("【SyncTable】==================== 表结构同步失败(无配置) ====================")
			return
		}
	}

	// 清理临时表
	zlog.Debug("【SyncTable】步骤7: 开始清理临时表")
	tmpTableName := tableau + "_tmp"
	zlog.Debug("【SyncTable】临时表名:", tmpTableName)

	if ok := conn.DropTable(tmpTableName); ok {
		zlog.Debug("【SyncTable】步骤7: 临时表清理成功 ✓")

		// 获取原表结构并创建临时表
		zlog.Debug("【SyncTable】步骤8: 获取配置表结构")
		originalSchema := conn.Tableschme(tableau)
		if len(originalSchema) == 0 {
			zlog.Error("【SyncTable】步骤8: 获取原表结构失败 ✗")
			zlog.Error("【SyncTable】失败原因: Tableschme方法返回空字符串")
			zlog.Error("【SyncTable】可能原因: 缓存中无表结构或表结构为空")
			SetSyncStatus(tableau, SyncFailGetSchema, GetSyncStatusMessage(SyncFailGetSchema))
			zlog.Error("【SyncTable】==================== 表结构同步失败 ====================")
			return
		}
		zlog.Debug("【SyncTable】步骤8: 获取原表结构成功 ✓")
		zlog.Debug("【SyncTable】原表结构长度:", len(originalSchema), "字节")

		// 替换表名创建临时表
		zlog.Debug("【SyncTable】步骤9: 创建临时表")

		// 使用更精确的表名替换，只替换CREATE TABLE语句中的表名
		tmpSchema := originalSchema
		// 替换 CREATE TABLE `tablename` 中的表名
		tmpSchema = strings.Replace(tmpSchema, "CREATE TABLE IF NOT EXISTS `"+tableau+"`", "CREATE TABLE IF NOT EXISTS `"+tmpTableName+"`", 1)
		tmpSchema = strings.Replace(tmpSchema, "CREATE TABLE `"+tableau+"`", "CREATE TABLE `"+tmpTableName+"`", 1)
		// 如果没有反引号的情况
		tmpSchema = strings.Replace(tmpSchema, "CREATE TABLE IF NOT EXISTS "+tableau+" ", "CREATE TABLE IF NOT EXISTS "+tmpTableName+" ", 1)
		tmpSchema = strings.Replace(tmpSchema, "CREATE TABLE "+tableau+" ", "CREATE TABLE "+tmpTableName+" ", 1)

		zlog.Debug("【SyncTable】临时表结构长度:", len(tmpSchema), "字节")
		zlog.Debug("【SyncTable】表名替换: ", tableau, "->", tmpTableName)
		zlog.Debug("【SyncTable】替换后SQL预览:", tmpSchema[:200], "...")

		if conn.Createtable(tmpSchema) {
			zlog.Debug("【SyncTable】步骤9: 临时表创建成功 ✓")
		} else {
			zlog.Error("【SyncTable】步骤9: 临时表创建失败 ✗")
			zlog.Error("【SyncTable】失败原因: SQL执行错误或语法错误")
			SetSyncStatus(tableau, SyncFailTempCreate, GetSyncStatusMessage(SyncFailTempCreate))
			zlog.Error("【SyncTable】==================== 表结构同步失败 ====================")
			return
		}
	} else {
		zlog.Error("【SyncTable】步骤7: 临时表清理失败 ✗")
		zlog.Error("【SyncTable】失败的临时表名:", tmpTableName)
		zlog.Error("【SyncTable】可能原因: 表不存在或权限不足")
		SetSyncStatus(tableau, SyncFailTempCleanup, GetSyncStatusMessage(SyncFailTempCleanup))
		zlog.Error("【SyncTable】==================== 表结构同步失败 ====================")
		return
	}

	// 同步表结构到当前表
	zlog.Debug("【SyncTable】步骤10: 开始同步表结构到当前表")
	zlog.Debug("【SyncTable】同步方向: ", tmpTableName, "->", tableau)

	if ok := conn.AlterColumn(tmpTableName, tableau); !ok {
		zlog.Error("【SyncTable】步骤10: 同步表结构失败 ✗")
		zlog.Error("【SyncTable】失败的同步: ", tmpTableName, "->", tableau)
		zlog.Error("【SyncTable】可能原因: 表结构差异无法修正或SQL执行失败")

		// 表结构同步失败
		SetSyncStatus(tableau, SyncFailSchemaSync, GetSyncStatusMessage(SyncFailSchemaSync))
		zlog.Error("【SyncTable】设置同步状态: 失败")
		zlog.Error("【SyncTable】状态码:", SyncFailSchemaSync)
		zlog.Error("【SyncTable】==================== 表结构同步失败 ====================")
	} else {
		zlog.Info("【SyncTable】步骤10: 同步表结构成功 ✓")
		zlog.Debug("【SyncTable】成功的同步: ", tmpTableName, "->", tableau)

		// 表结构同步完成
		SetSyncStatus(tableau, SyncSuccessTempTable, GetSyncStatusMessage(SyncSuccessTempTable))
		zlog.Debug("【SyncTable】设置同步状态: 成功")
		zlog.Debug("【SyncTable】状态码:", SyncSuccessTempTable)

		// 🎯 设置验证标记，避免频繁创建临时表
		verifiedKey := tableau + "_verified"
		Role.Store(verifiedKey, "true")
		zlog.Debug("【SyncTable】设置数据库结构验证标记:", verifiedKey)

		zlog.Info("【SyncTable】==================== 表结构同步成功 ====================")
	}

	// 获取最终状态用于日志
	zlog.Debug("【SyncTable】步骤11: 获取最终同步状态")
	finalStatus := "unknown"
	finalCode := int64(0)
	finalMessage := ""

	if syncInfo, ok := SyncStatus[tableau]; ok {
		finalStatus = syncInfo.Status
		finalCode = syncInfo.Code
		finalMessage = syncInfo.Message
		zlog.Debug("【SyncTable】最终状态详情:")
		zlog.Debug("【SyncTable】  - 状态:", finalStatus)
		zlog.Debug("【SyncTable】  - 状态码:", finalCode)
		zlog.Debug("【SyncTable】  - 消息:", finalMessage)
		zlog.Debug("【SyncTable】  - 更新时间:", syncInfo.UpdateTime)
	} else {
		zlog.Warn("【SyncTable】警告: 无法获取最终同步状态")
	}

	zlog.Debug("【SyncTable】步骤12: SyncTable方法执行完成")
	zlog.Debug("【SyncTable】表名:", tableau, "最终状态:", finalStatus)

	// 🎯 新增：同步归档表结构
	zlog.Info("【SyncTable】步骤13: 开始同步归档表结构")
	conn.SyncArchiveTables(tableau)
	zlog.Info("【SyncTable】步骤13: 归档表结构同步完成")
}

// SyncArchiveTables 同步归档表结构
func (conn *Conn) SyncArchiveTables(mainTable string) {
	zlog.Info("【归档表同步】==================== 开始同步归档表结构 ====================")
	zlog.Debug("【归档表同步】步骤1: 进入SyncArchiveTables方法")
	zlog.Debug("【归档表同步】主表名:", mainTable)

	// 查询所有相关的归档表，排除临时表
	querySQL := fmt.Sprintf("SHOW TABLES LIKE '%s_2%%'", mainTable)
	zlog.Debug("【归档表同步】步骤2: 查询归档表SQL:", querySQL)

	archiveTables, err := conn.ReadSql(querySQL)
	if err != nil {
		zlog.Error("【归档表同步】查询归档表失败，主表:", mainTable, "错误:", err.Error())
		return
	}

	zlog.Info("【归档表同步】步骤2: 查询归档表完成，主表:", mainTable, "找到归档表数量:", len(archiveTables))

	if len(archiveTables) == 0 {
		zlog.Info("【归档表同步】未找到归档表，主表:", mainTable, "跳过归档表同步")
		zlog.Info("【归档表同步】==================== 归档表同步完成(无归档表) ====================")
		return
	}

	// 获取主表的表结构配置
	mainTableSchema := conn.Tableschme(mainTable)
	if mainTableSchema == "" {
		zlog.Error("【归档表同步】无法获取主表结构配置，主表:", mainTable)
		return
	}

	zlog.Debug("【归档表同步】步骤3: 获取主表结构成功，主表:", mainTable, "结构长度:", len(mainTableSchema))

	// 遍历每个归档表进行结构同步
	successCount := 0
	failCount := 0

	for i, row := range archiveTables {
		// 获取表名（SHOW TABLES 返回的列名可能是 "Tables_in_数据库名"）
		var archiveTableName string
		for _, value := range row {
			archiveTableName = value
			break // 取第一个值作为表名
		}

		if archiveTableName == "" {
			zlog.Error("【归档表同步】无法获取归档表名，跳过，索引:", i)
			failCount++
			continue
		}

		// 跳过临时表，避免对临时表再创建临时表
		if strings.HasSuffix(archiveTableName, "_tmp") {
			zlog.Debug("【归档表同步】跳过临时表:", archiveTableName)
			continue
		}

		zlog.Info("【归档表同步】步骤4.", i+1, ": 开始同步归档表，表名:", archiveTableName)

		// 使用主表的结构同步归档表
		if conn.syncSingleArchiveTable(mainTable, archiveTableName, mainTableSchema) {
			successCount++
			zlog.Info("【归档表同步】归档表同步成功，表名:", archiveTableName)
		} else {
			failCount++
			zlog.Error("【归档表同步】归档表同步失败，表名:", archiveTableName)
		}
	}

	zlog.Info("【归档表同步】步骤5: 归档表同步统计，主表:", mainTable)
	zlog.Info("【归档表同步】  - 总数:", len(archiveTables))
	zlog.Info("【归档表同步】  - 成功:", successCount)
	zlog.Info("【归档表同步】  - 失败:", failCount)
	zlog.Info("【归档表同步】==================== 归档表结构同步完成 ====================")
}

// syncSingleArchiveTable 同步单个归档表结构（优化版本）
func (conn *Conn) syncSingleArchiveTable(mainTable, archiveTable, mainTableSchema string) bool {
	zlog.Debug("【单表同步】开始同步单个归档表，主表:", mainTable, "归档表:", archiveTable)

	tmpTableName := mainTable + "_tmp"

	// 步骤1: 确保临时表存在
	zlog.Debug("【单表同步】步骤1: 确保临时表存在")
	if !conn.ensureTmpTableExists(mainTable, mainTableSchema) {
		zlog.Error("【单表同步】确保临时表存在失败，主表:", mainTable)
		return false
	}

	// 步骤2: 检查DDL与临时表是否有差异
	zlog.Debug("【单表同步】步骤2: 检查DDL与临时表差异")
	if conn.checkDDLVsTmpTable(mainTable, mainTableSchema) {
		zlog.Info("【单表同步】检测到DDL变更，更新临时表:", tmpTableName)
		if !conn.updateTmpTableFromDDL(tmpTableName, mainTableSchema) {
			zlog.Error("【单表同步】更新临时表失败:", tmpTableName)
			return false
		}
	} else {
		zlog.Debug("【单表同步】DDL无变更，跳过临时表更新:", tmpTableName)
	}

	// 步骤3: 检查临时表与归档表是否有差异
	zlog.Debug("【单表同步】步骤3: 检查临时表与归档表差异")
	zlog.Info("【单表同步】🎯 开始差异检查，临时表:", tmpTableName, "归档表:", archiveTable)

	// 🎯 新增：检查表是否存在
	if !conn.TableExist(tmpTableName) {
		zlog.Error("【单表同步】❌ 临时表不存在:", tmpTableName)
		return false
	}
	if !conn.TableExist(archiveTable) {
		zlog.Error("【单表同步】❌ 归档表不存在:", archiveTable)
		return false
	}
	zlog.Info("【单表同步】✅ 两个表都存在，开始差异检查")

	hasDifference := conn.checkTmpVsArchiveTable(tmpTableName, archiveTable)
	zlog.Info("【单表同步】🎯 差异检查结果:", hasDifference, "临时表:", tmpTableName, "归档表:", archiveTable)

	if hasDifference {
		zlog.Info("【单表同步】✅ 检测到结构差异，开始同步归档表:", archiveTable)
		syncResult := conn.AlterColumn(tmpTableName, archiveTable)
		if !syncResult {
			zlog.Error("【单表同步】❌ 同步归档表失败:", archiveTable)
			return false
		}
		zlog.Info("【单表同步】✅ 归档表同步成功:", archiveTable)
	} else {
		zlog.Info("【单表同步】⚠️  结构无差异，跳过归档表同步:", archiveTable)
	}

	zlog.Debug("【单表同步】单个归档表同步完成，归档表:", archiveTable)
	return true
}

// ensureTmpTableExists 确保临时表存在
func (conn *Conn) ensureTmpTableExists(mainTable, mainTableSchema string) bool {
	tmpTableName := mainTable + "_tmp"
	zlog.Debug("【临时表检查】检查临时表是否存在:", tmpTableName)

	// 1. 检查临时表是否存在
	if !conn.TableExist(tmpTableName) {
		zlog.Info("【临时表检查】临时表不存在，开始创建:", tmpTableName)
		// 临时表不存在，创建它
		return conn.createTmpTableFromDDL(tmpTableName, mainTableSchema)
	}

	zlog.Debug("【临时表检查】临时表已存在，无需创建:", tmpTableName)
	return true
}

// createTmpTableFromDDL 从DDL创建临时表
func (conn *Conn) createTmpTableFromDDL(tmpTableName, mainTableSchema string) bool {
	zlog.Info("【临时表创建】==================== 开始从DDL创建临时表 ====================")
	zlog.Info("【临时表创建】目标临时表名:", tmpTableName)
	zlog.Info("【临时表创建】原始DDL长度:", len(mainTableSchema), "字节")

	// 记录DDL内容的关键信息
	if len(mainTableSchema) > 0 {
		// 显示DDL的前200个字符用于调试
		ddlPreview := mainTableSchema
		if len(ddlPreview) > 200 {
			ddlPreview = ddlPreview[:200] + "..."
		}
		zlog.Debug("【临时表创建】DDL内容预览:", ddlPreview)

		// 统计DDL中的关键信息
		fieldCount := strings.Count(mainTableSchema, "varchar") + strings.Count(mainTableSchema, "int") +
			strings.Count(mainTableSchema, "decimal") + strings.Count(mainTableSchema, "text") +
			strings.Count(mainTableSchema, "datetime") + strings.Count(mainTableSchema, "timestamp")
		zlog.Info("【临时表创建】DDL分析 - 估计字段数量:", fieldCount)
	} else {
		zlog.Error("【临时表创建】DDL内容为空，无法创建临时表")
		zlog.Error("【临时表创建】错误详情: mainTableSchema参数为空字符串")
		return false
	}

	// 将主表结构中的表名替换为临时表名
	tmpTableSchema := mainTableSchema

	// 提取主表名（从DDL中解析）
	zlog.Debug("【临时表创建】步骤1: 开始提取主表名")
	mainTableName := conn.extractTableNameFromDDL(mainTableSchema)
	if mainTableName == "" {
		zlog.Error("【临时表创建】无法从DDL中提取主表名")
		zlog.Error("【临时表创建】错误详情: extractTableNameFromDDL返回空字符串")
		zlog.Error("【临时表创建】DDL内容分析:")

		// 分析DDL内容，查找可能的问题
		if !strings.Contains(mainTableSchema, "CREATE TABLE") {
			zlog.Error("【临时表创建】  - DDL中不包含'CREATE TABLE'关键字")
		}
		if !strings.Contains(mainTableSchema, "`") {
			zlog.Error("【临时表创建】  - DDL中不包含反引号，可能表名格式不正确")
		}

		// 显示DDL的开头部分用于调试
		if len(mainTableSchema) > 100 {
			zlog.Error("【临时表创建】DDL开头100字符:", mainTableSchema[:100])
		} else {
			zlog.Error("【临时表创建】完整DDL内容:", mainTableSchema)
		}
		return false
	}
	zlog.Info("【临时表创建】步骤1: 成功提取主表名, 主表名:", mainTableName)

	// 替换表名
	zlog.Debug("【临时表创建】步骤2: 开始替换表名")
	originalSchema := tmpTableSchema
	tmpTableSchema = strings.Replace(tmpTableSchema, "`"+mainTableName+"`", "`"+tmpTableName+"`", 1)
	tmpTableSchema = strings.Replace(tmpTableSchema, "CREATE TABLE `"+mainTableName+"`", "CREATE TABLE `"+tmpTableName+"`", 1)

	// 验证表名替换是否成功
	if tmpTableSchema == originalSchema {
		zlog.Warn("【临时表创建】表名替换可能未生效")
		zlog.Debug("【临时表创建】原始表名模式1: `" + mainTableName + "`")
		zlog.Debug("【临时表创建】原始表名模式2: CREATE TABLE `" + mainTableName + "`")
	} else {
		zlog.Info("【临时表创建】步骤2: 表名替换成功, 替换:", mainTableName, "->", tmpTableName)
	}

	// 确保使用CREATE TABLE IF NOT EXISTS
	zlog.Debug("【临时表创建】步骤3: 确保使用IF NOT EXISTS")
	if !strings.Contains(tmpTableSchema, "IF NOT EXISTS") {
		originalSchema = tmpTableSchema
		tmpTableSchema = strings.Replace(tmpTableSchema, "CREATE TABLE", "CREATE TABLE IF NOT EXISTS", 1)
		if tmpTableSchema != originalSchema {
			zlog.Info("【临时表创建】步骤3: 已添加IF NOT EXISTS")
		} else {
			zlog.Warn("【临时表创建】步骤3: IF NOT EXISTS添加可能失败")
		}
	} else {
		zlog.Debug("【临时表创建】步骤3: DDL已包含IF NOT EXISTS")
	}

	zlog.Info("【临时表创建】步骤4: 准备执行SQL")
	zlog.Info("【临时表创建】最终SQL长度:", len(tmpTableSchema), "字节")

	// 显示最终SQL的关键部分
	if len(tmpTableSchema) > 0 {
		sqlPreview := tmpTableSchema
		if len(sqlPreview) > 300 {
			sqlPreview = sqlPreview[:300] + "..."
		}
		zlog.Debug("【临时表创建】最终SQL预览:", sqlPreview)
	}

	// 执行SQL创建临时表
	zlog.Info("【临时表创建】步骤5: 执行SQL创建临时表")
	if !conn.SqlAction(tmpTableSchema) {
		zlog.Error("【临时表创建】创建临时表失败:", tmpTableName)
		zlog.Error("【临时表创建】失败详情:")
		zlog.Error("【临时表创建】  - 目标表名:", tmpTableName)
		zlog.Error("【临时表创建】  - SQL长度:", len(tmpTableSchema))
		zlog.Error("【临时表创建】  - SqlAction方法返回false")

		// 记录可能的失败原因
		zlog.Error("【临时表创建】可能的失败原因:")
		zlog.Error("【临时表创建】  1. SQL语法错误")
		zlog.Error("【临时表创建】  2. 数据库连接问题")
		zlog.Error("【临时表创建】  3. 权限不足")
		zlog.Error("【临时表创建】  4. 表名冲突")
		zlog.Error("【临时表创建】  5. DDL内容格式错误")

		// 显示完整的SQL用于调试（如果不太长）
		if len(tmpTableSchema) <= 1000 {
			zlog.Error("【临时表创建】完整SQL内容:", tmpTableSchema)
		} else {
			zlog.Error("【临时表创建】SQL太长，显示前500字符:", tmpTableSchema[:500])
			zlog.Error("【临时表创建】SQL后500字符:", tmpTableSchema[len(tmpTableSchema)-500:])
		}

		return false
	}
	zlog.Info("【临时表创建】步骤5: SQL执行成功")

	// 验证临时表是否真的创建成功
	zlog.Info("【临时表创建】步骤6: 验证临时表是否真的存在")
	if !conn.TableExist(tmpTableName) {
		zlog.Error("【临时表创建】验证失败：临时表不存在:", tmpTableName)
		zlog.Error("【临时表创建】验证失败详情:")
		zlog.Error("【临时表创建】  - SQL执行返回成功，但表不存在")
		return false
	}
	zlog.Info("【临时表创建】步骤6: 临时表存在性验证成功")

	// 验证临时表结构是否正确（检查关键字段）
	zlog.Info("【临时表创建】步骤7: 验证临时表结构是否正确")
	if !conn.verifyTableStructure(tmpTableName, mainTableSchema) {
		zlog.Error("【临时表创建】验证失败：临时表结构不正确:", tmpTableName)
		zlog.Error("【临时表创建】结构验证失败详情:")
		zlog.Error("【临时表创建】  - 临时表已创建但结构不符合预期")
		return false
	}
	zlog.Info("【临时表创建】步骤7: 临时表结构验证成功")

	zlog.Info("【临时表创建】临时表创建并验证成功:", tmpTableName)
	zlog.Info("【临时表创建】==================== 临时表创建完成 ====================")
	return true
}

// extractTableNameFromDDL 从DDL中提取表名
func (conn *Conn) extractTableNameFromDDL(ddl string) string {
	zlog.Debug("【表名提取】开始从DDL中提取表名")
	zlog.Debug("【表名提取】DDL长度:", len(ddl), "字节")

	if len(ddl) == 0 {
		zlog.Error("【表名提取】DDL为空，无法提取表名")
		return ""
	}

	// 显示DDL的开头部分用于调试
	ddlPreview := ddl
	if len(ddlPreview) > 150 {
		ddlPreview = ddlPreview[:150] + "..."
	}
	zlog.Debug("【表名提取】DDL开头内容:", ddlPreview)

	// 使用正则表达式提取CREATE TABLE语句中的表名（带反引号）
	zlog.Debug("【表名提取】尝试方法1: 匹配带反引号的表名")
	re := regexp.MustCompile(`CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?` + "`" + `([^` + "`" + `]+)` + "`")
	matches := re.FindStringSubmatch(ddl)
	if len(matches) > 1 {
		tableName := matches[1]
		zlog.Info("【表名提取】方法1成功提取表名:", tableName)
		return tableName
	}
	zlog.Debug("【表名提取】方法1失败: 未找到带反引号的表名")

	// 如果没有反引号，尝试匹配不带反引号的表名
	zlog.Debug("【表名提取】尝试方法2: 匹配不带反引号的表名")
	re2 := regexp.MustCompile(`CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?([^\s\(]+)`)
	matches2 := re2.FindStringSubmatch(ddl)
	if len(matches2) > 1 {
		tableName := matches2[1]
		zlog.Info("【表名提取】方法2成功提取表名:", tableName)
		return tableName
	}
	zlog.Debug("【表名提取】方法2失败: 未找到不带反引号的表名")

	// 所有方法都失败，进行详细分析
	zlog.Error("【表名提取】所有方法都失败，无法提取表名")
	zlog.Error("【表名提取】DDL分析:")

	// 检查DDL中是否包含关键字
	if !strings.Contains(strings.ToUpper(ddl), "CREATE") {
		zlog.Error("【表名提取】  - DDL中不包含'CREATE'关键字")
	}
	if !strings.Contains(strings.ToUpper(ddl), "TABLE") {
		zlog.Error("【表名提取】  - DDL中不包含'TABLE'关键字")
	}

	// 查找CREATE TABLE的位置
	createPos := strings.Index(strings.ToUpper(ddl), "CREATE TABLE")
	if createPos >= 0 {
		zlog.Error("【表名提取】  - 找到'CREATE TABLE'位置:", createPos)
		// 显示CREATE TABLE后面的内容
		afterCreate := ddl[createPos:]
		if len(afterCreate) > 100 {
			afterCreate = afterCreate[:100] + "..."
		}
		zlog.Error("【表名提取】  - CREATE TABLE后的内容:", afterCreate)
	} else {
		zlog.Error("【表名提取】  - 未找到'CREATE TABLE'")
	}

	return ""
}

// verifyTableStructure 验证表结构是否正确
func (conn *Conn) verifyTableStructure(tableName, expectedDDL string) bool {
	zlog.Info("【结构验证】==================== 开始验证表结构 ====================")
	zlog.Info("【结构验证】验证表名:", tableName)

	// 1. 检查表是否存在
	if !conn.TableExist(tableName) {
		zlog.Error("【结构验证】表不存在:", tableName)
		return false
	}
	zlog.Debug("【结构验证】表存在性检查通过:", tableName)

	// 2. 获取表的实际字段列表
	actualColumns, err := conn.getTableColumns(tableName)
	if err != nil {
		zlog.Error("【结构验证】获取表字段失败:", tableName, "错误:", err.Error())
		return false
	}
	zlog.Info("【结构验证】实际字段数量:", len(actualColumns))

	// 3. 从DDL中提取期望的字段列表
	expectedColumns := conn.extractColumnsFromDDL(expectedDDL)
	zlog.Info("【结构验证】期望字段数量:", len(expectedColumns))

	// 4. 验证关键字段是否存在
	missingColumns := []string{}
	for _, expectedCol := range expectedColumns {
		found := false
		for _, actualCol := range actualColumns {
			if actualCol == expectedCol {
				found = true
				break
			}
		}
		if !found {
			missingColumns = append(missingColumns, expectedCol)
		}
	}

	if len(missingColumns) > 0 {
		zlog.Error("【结构验证】缺失字段:", missingColumns)
		zlog.Error("【结构验证】期望字段:", expectedColumns)
		zlog.Error("【结构验证】实际字段:", actualColumns)
		return false
	}

	zlog.Info("【结构验证】所有期望字段都存在，验证通过:", tableName)
	zlog.Info("【结构验证】==================== 表结构验证完成 ====================")
	return true
}

// getTableColumns 获取表的字段列表
func (conn *Conn) getTableColumns(tableName string) ([]string, error) {
	dbName := Mysqldb.Database
	sql := fmt.Sprintf(`
		SELECT COLUMN_NAME
		FROM information_schema.COLUMNS
		WHERE table_schema = '%s' AND table_name = '%s'
		ORDER BY ORDINAL_POSITION
	`, dbName, tableName)

	result, err := conn.ReadSql(sql)
	if err != nil {
		return nil, err
	}

	columns := make([]string, 0, len(result))
	for _, row := range result {
		if columnName, ok := row["COLUMN_NAME"]; ok {
			columns = append(columns, columnName)
		}
	}

	return columns, nil
}

// extractColumnsFromDDL 从DDL中提取字段名列表
func (conn *Conn) extractColumnsFromDDL(ddl string) []string {
	columns := []string{}

	// 按行分割DDL
	lines := strings.Split(ddl, "\n")
	inTableDefinition := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 检查是否进入表定义部分
		if strings.Contains(line, "CREATE TABLE") {
			inTableDefinition = true
			continue
		}

		// 如果遇到表定义结束，停止解析
		if inTableDefinition && (strings.HasPrefix(line, ")") || strings.Contains(line, "ENGINE=")) {
			break
		}

		// 只在表定义内部解析字段
		if inTableDefinition {
			// 跳过索引定义行
			if strings.HasPrefix(line, "PRIMARY KEY") ||
				strings.HasPrefix(line, "KEY ") ||
				strings.HasPrefix(line, "INDEX ") ||
				strings.HasPrefix(line, "UNIQUE KEY") ||
				strings.HasPrefix(line, "CONSTRAINT") ||
				line == "" {
				continue
			}

			// 匹配字段定义：`字段名` 类型定义
			re := regexp.MustCompile("^`([^`]+)`\\s+")
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				columnName := matches[1]
				// 确保不是表名（在CREATE TABLE行中）
				if columnName != conn.extractTableNameFromDDL(ddl) {
					columns = append(columns, columnName)
				}
			}
		}
	}

	return columns
}

// quickStructureCheck 快速检查表结构是否与配置一致（只检查字段数量）
func (conn *Conn) quickStructureCheck(tableName, ddl string) bool {
	zlog.Debug("【快速检查】开始快速结构检查，表名:", tableName)

	// 获取数据库中实际的字段数量
	actualColumns, err := conn.getTableColumns(tableName)
	if err != nil {
		zlog.Error("【快速检查】获取实际字段失败:", err.Error())
		return false
	}

	// 从DDL中提取期望的字段数量
	expectedColumns := conn.extractColumnsFromDDL(ddl)

	zlog.Debug("【快速检查】实际字段数量:", len(actualColumns))
	zlog.Debug("【快速检查】期望字段数量:", len(expectedColumns))

	// 如果字段数量不同，肯定需要同步
	if len(actualColumns) != len(expectedColumns) {
		zlog.Warn("【快速检查】字段数量不匹配，实际:", len(actualColumns), "期望:", len(expectedColumns))
		return false
	}

	// 字段数量相同，认为结构可能一致（这是快速检查，不做详细对比）
	zlog.Debug("【快速检查】字段数量匹配，认为结构一致")
	return true
}

// verifyTableSyncResult 验证表结构同步结果
func (conn *Conn) verifyTableSyncResult(srcTable, destTable string) bool {
	zlog.Info("【同步验证】==================== 开始验证表结构同步结果 ====================")
	zlog.Info("【同步验证】源表:", srcTable)
	zlog.Info("【同步验证】目标表:", destTable)

	// 1. 检查两个表是否都存在
	if !conn.TableExist(srcTable) {
		zlog.Error("【同步验证】源表不存在:", srcTable)
		return false
	}
	if !conn.TableExist(destTable) {
		zlog.Error("【同步验证】目标表不存在:", destTable)
		return false
	}
	zlog.Debug("【同步验证】两个表都存在，开始结构对比")

	// 2. 获取两个表的字段列表
	srcColumns, err := conn.getTableColumns(srcTable)
	if err != nil {
		zlog.Error("【同步验证】获取源表字段失败:", srcTable, "错误:", err.Error())
		return false
	}

	destColumns, err := conn.getTableColumns(destTable)
	if err != nil {
		zlog.Error("【同步验证】获取目标表字段失败:", destTable, "错误:", err.Error())
		return false
	}

	zlog.Info("【同步验证】源表字段数量:", len(srcColumns))
	zlog.Info("【同步验证】目标表字段数量:", len(destColumns))

	// 3. 检查源表的所有字段是否都在目标表中存在
	missingInDest := []string{}
	for _, srcCol := range srcColumns {
		found := false
		for _, destCol := range destColumns {
			if srcCol == destCol {
				found = true
				break
			}
		}
		if !found {
			missingInDest = append(missingInDest, srcCol)
		}
	}

	if len(missingInDest) > 0 {
		zlog.Error("【同步验证】目标表缺失字段:", missingInDest)
		zlog.Error("【同步验证】源表字段:", srcColumns)
		zlog.Error("【同步验证】目标表字段:", destColumns)
		return false
	}

	// 4. 进一步验证字段类型是否一致
	if !conn.verifyColumnTypes(srcTable, destTable) {
		zlog.Error("【同步验证】字段类型验证失败")
		return false
	}

	zlog.Info("【同步验证】表结构同步验证成功，所有字段都已正确同步")
	zlog.Info("【同步验证】==================== 表结构同步验证完成 ====================")
	return true
}

// verifyColumnTypes 验证两个表的字段类型是否一致
func (conn *Conn) verifyColumnTypes(srcTable, destTable string) bool {
	zlog.Debug("【类型验证】开始验证字段类型一致性")

	dbName := Mysqldb.Database
	sql := fmt.Sprintf(`
		SELECT
			src.COLUMN_NAME,
			src.COLUMN_TYPE as src_type,
			dest.COLUMN_TYPE as dest_type
		FROM
			(SELECT COLUMN_NAME, COLUMN_TYPE FROM information_schema.COLUMNS
			 WHERE table_schema = '%s' AND table_name = '%s') src
		INNER JOIN
			(SELECT COLUMN_NAME, COLUMN_TYPE FROM information_schema.COLUMNS
			 WHERE table_schema = '%s' AND table_name = '%s') dest
		ON src.COLUMN_NAME = dest.COLUMN_NAME
		WHERE src.COLUMN_TYPE != dest.COLUMN_TYPE
	`, dbName, srcTable, dbName, destTable)

	result, err := conn.ReadSql(sql)
	if err != nil {
		zlog.Error("【类型验证】查询字段类型差异失败:", err.Error())
		return false
	}

	if len(result) > 0 {
		zlog.Error("【类型验证】发现字段类型不一致:")
		for i, row := range result {
			columnName := row["COLUMN_NAME"]
			srcType := row["src_type"]
			destType := row["dest_type"]
			zlog.Error("【类型验证】差异", i+1, ":", columnName, "源类型:", srcType, "目标类型:", destType)
		}
		return false
	}

	zlog.Debug("【类型验证】所有字段类型都一致")
	return true
}

// checkDDLVsTmpTable 检查DDL与临时表是否有差异
func (conn *Conn) checkDDLVsTmpTable(mainTable, mainTableSchema string) bool {
	tmpTableName := mainTable + "_tmp"
	zlog.Info("【DDL差异检查】==================== 开始DDL与临时表差异检查 ====================")
	zlog.Info("【DDL差异检查】主表:", mainTable)
	zlog.Info("【DDL差异检查】临时表:", tmpTableName)
	zlog.Debug("【DDL差异检查】DDL长度:", len(mainTableSchema), "字节")

	// 检查临时表是否存在
	if !conn.TableExist(tmpTableName) {
		zlog.Error("【DDL差异检查】临时表不存在，无法进行对比:", tmpTableName)
		return false
	}
	zlog.Debug("【DDL差异检查】临时表存在性检查通过:", tmpTableName)

	// 创建一个虚拟临时表来对比
	virtualTmpName := mainTable + "_virtual_tmp"
	zlog.Info("【DDL差异检查】创建虚拟临时表用于对比:", virtualTmpName)

	// 1. 创建虚拟临时表（用于对比，不实际使用）
	if !conn.createTmpTableFromDDL(virtualTmpName, mainTableSchema) {
		zlog.Error("【DDL差异检查】创建虚拟临时表失败:", virtualTmpName)
		return false
	}
	zlog.Info("【DDL差异检查】虚拟临时表创建成功:", virtualTmpName)

	// 2. 对比虚拟临时表与实际临时表
	zlog.Info("【DDL差异检查】开始对比虚拟临时表与实际临时表")
	zlog.Info("【DDL差异检查】对比参数: 源表(虚拟)=", virtualTmpName, ", 目标表(实际)=", tmpTableName)
	hasDiff := conn.hasStructureDifference(virtualTmpName, tmpTableName)

	// 3. 清理虚拟临时表
	zlog.Debug("【DDL差异检查】清理虚拟临时表:", virtualTmpName)
	conn.DropTable(virtualTmpName)

	if hasDiff {
		zlog.Info("【DDL差异检查】✓ 检测到DDL与临时表有差异，需要更新临时表:", tmpTableName)
	} else {
		zlog.Info("【DDL差异检查】✗ DDL与临时表结构一致，无需更新:", tmpTableName)
	}

	zlog.Info("【DDL差异检查】==================== DDL与临时表差异检查完成 ====================")
	return hasDiff
}

// hasStructureDifference 检查两个表是否有结构差异
func (conn *Conn) hasStructureDifference(srcTable, destTable string) bool {
	zlog.Info("【结构差异检查】==================== 开始表结构差异检查 ====================")
	zlog.Info("【结构差异检查】源表:", srcTable)
	zlog.Info("【结构差异检查】目标表:", destTable)

	// 先检查两个表是否都存在
	if !conn.TableExist(srcTable) {
		zlog.Error("【结构差异检查】源表不存在:", srcTable)
		return false
	}
	if !conn.TableExist(destTable) {
		zlog.Error("【结构差异检查】目标表不存在:", destTable)
		return false
	}
	zlog.Debug("【结构差异检查】两个表都存在，开始结构对比")

	// 使用与AlterColumn相同的逻辑，但只返回是否有差异
	// 动态获取数据库名称，而不是硬编码
	dbName := Mysqldb.Database
	zlog.Debug("【结构差异检查】使用数据库名称:", dbName)

	sqlstr := fmt.Sprintf(`
        select count(*) as diff_count
        from (
            select COLUMN_NAME, COLUMN_TYPE
            from information_schema.COLUMNS
            where table_schema = '%s' and table_name = '%s'
        ) t1
        left join (
            select COLUMN_NAME, COLUMN_TYPE
            from information_schema.COLUMNS
            where table_schema = '%s' and table_name = '%s'
        ) t2 on t1.COLUMN_NAME = t2.COLUMN_NAME
        where t1.COLUMN_TYPE != t2.COLUMN_TYPE
           or t2.COLUMN_NAME is null
           or t2.COLUMN_TYPE is null
    `, dbName, srcTable, dbName, destTable)

	zlog.Debug("【结构差异检查】执行差异查询SQL")
	zlog.Debug("【结构差异检查】SQL:", sqlstr)

	// 🎯 新增：先分别查询两个表的字段信息进行对比
	zlog.Info("【差异调试】==================== 开始详细差异分析 ====================")

	// 查询源表字段
	srcFieldsSQL := fmt.Sprintf(`
		SELECT COLUMN_NAME, COLUMN_TYPE, ORDINAL_POSITION
		FROM information_schema.COLUMNS
		WHERE table_schema = '%s' AND table_name = '%s'
		ORDER BY ORDINAL_POSITION
	`, dbName, srcTable)

	srcFields, err := conn.ReadSql(srcFieldsSQL)
	if err != nil {
		zlog.Error("【差异调试】查询源表字段失败:", err.Error())
	} else {
		zlog.Info("【差异调试】源表字段数量:", len(srcFields))
		zlog.Debug("【差异调试】源表前5个字段:")
		for i, field := range srcFields {
			if i < 5 {
				zlog.Debug("【差异调试】源表字段", i+1, ":", field["COLUMN_NAME"], field["COLUMN_TYPE"])
			}
		}
		if len(srcFields) > 5 {
			zlog.Debug("【差异调试】源表最后5个字段:")
			for i := len(srcFields) - 5; i < len(srcFields); i++ {
				if i >= 0 {
					zlog.Debug("【差异调试】源表字段", i+1, ":", srcFields[i]["COLUMN_NAME"], srcFields[i]["COLUMN_TYPE"])
				}
			}
		}
	}

	// 查询目标表字段
	destFieldsSQL := fmt.Sprintf(`
		SELECT COLUMN_NAME, COLUMN_TYPE, ORDINAL_POSITION
		FROM information_schema.COLUMNS
		WHERE table_schema = '%s' AND table_name = '%s'
		ORDER BY ORDINAL_POSITION
	`, dbName, destTable)

	destFields, err := conn.ReadSql(destFieldsSQL)
	if err != nil {
		zlog.Error("【差异调试】查询目标表字段失败:", err.Error())
	} else {
		zlog.Info("【差异调试】目标表字段数量:", len(destFields))
		zlog.Debug("【差异调试】目标表前5个字段:")
		for i, field := range destFields {
			if i < 5 {
				zlog.Debug("【差异调试】目标表字段", i+1, ":", field["COLUMN_NAME"], field["COLUMN_TYPE"])
			}
		}
		if len(destFields) > 5 {
			zlog.Debug("【差异调试】目标表最后5个字段:")
			for i := len(destFields) - 5; i < len(destFields); i++ {
				if i >= 0 {
					zlog.Debug("【差异调试】目标表字段", i+1, ":", destFields[i]["COLUMN_NAME"], destFields[i]["COLUMN_TYPE"])
				}
			}
		}
	}

	// 执行原始差异查询
	result, err := conn.ReadSql(sqlstr)
	if err != nil {
		zlog.Error("【结构差异检查】查询表结构差异失败:", err.Error())
		zlog.Error("【结构差异检查】SQL:", sqlstr)
		return false
	}

	if len(result) == 0 {
		zlog.Warn("【结构差异检查】查询结果为空，认为无差异")
		return false
	}

	zlog.Debug("【结构差异检查】查询结果:", result)

	diffCountStr, ok := result[0]["diff_count"]
	if !ok {
		zlog.Error("【结构差异检查】无法获取差异计数，结果:", result[0])
		return false
	}

	diffCount, err := strconv.Atoi(diffCountStr)
	if err != nil {
		zlog.Error("【结构差异检查】差异计数转换失败:", err.Error(), "原始值:", diffCountStr)
		return false
	}

	zlog.Info("【结构差异检查】差异计数:", diffCount)

	// 🎯 新增：无论是否有差异都进行详细分析
	zlog.Info("【差异调试】==================== 开始手动差异对比 ====================")

	// 手动对比字段差异
	if len(srcFields) > 0 && len(destFields) > 0 {
		// 创建目标表字段映射
		destFieldMap := make(map[string]string)
		for _, field := range destFields {
			destFieldMap[field["COLUMN_NAME"]] = field["COLUMN_TYPE"]
		}

		// 检查源表中每个字段在目标表中的情况
		manualDiffCount := 0
		zlog.Info("【差异调试】开始逐字段对比:")

		for i, srcField := range srcFields {
			srcName := srcField["COLUMN_NAME"]
			srcType := srcField["COLUMN_TYPE"]

			if destType, exists := destFieldMap[srcName]; exists {
				// 字段存在，检查类型是否一致
				if srcType != destType {
					manualDiffCount++
					zlog.Warn("【差异调试】字段类型不一致:", srcName, "源表:", srcType, "目标表:", destType)
				} else {
					if i < 3 || i >= len(srcFields)-3 { // 只显示前3个和后3个字段的匹配情况
						zlog.Debug("【差异调试】字段匹配:", srcName, "类型:", srcType)
					}
				}
			} else {
				// 字段在目标表中不存在
				manualDiffCount++
				zlog.Warn("【差异调试】字段缺失:", srcName, "类型:", srcType, "在目标表中不存在")
			}
		}

		zlog.Info("【差异调试】手动对比结果: 发现", manualDiffCount, "个差异")
		zlog.Info("【差异调试】SQL查询结果: 发现", diffCount, "个差异")

		if manualDiffCount != diffCount {
			zlog.Error("【差异调试】⚠️  手动对比与SQL查询结果不一致！")
			zlog.Error("【差异调试】这可能是SQL查询逻辑的问题")
		}
	}

	zlog.Info("【差异调试】==================== 差异分析完成 ====================")

	// 如果有差异，查询具体的差异详情
	if diffCount > 0 {
		zlog.Info("【结构差异检查】发现", diffCount, "个差异，查询详细差异信息")
		conn.logDetailedDifferences(srcTable, destTable)
	}

	zlog.Info("【结构差异检查】==================== 表结构差异检查完成 ====================")
	return diffCount > 0
}

// logDetailedDifferences 记录详细的表结构差异信息
func (conn *Conn) logDetailedDifferences(srcTable, destTable string) {
	zlog.Info("【差异详情】==================== 开始记录详细差异信息 ====================")

	// 查询详细的差异信息
	detailSql := fmt.Sprintf(`
        select
            t1.COLUMN_NAME as src_column,
            t1.COLUMN_TYPE as src_type,
            t2.COLUMN_NAME as dest_column,
            t2.COLUMN_TYPE as dest_type,
            case
                when t2.COLUMN_NAME is null then 'ADD'
                when t1.COLUMN_TYPE != t2.COLUMN_TYPE then 'CHANGE'
                else 'UNKNOWN'
            end as action_type
        from
            (select COLUMN_NAME, COLUMN_TYPE
             from information_schema.COLUMNS
             where table_schema = 'gstation' and table_name = '%s') t1
        left join
            (select COLUMN_NAME, COLUMN_TYPE
             from information_schema.COLUMNS
             where table_schema = 'gstation' and table_name = '%s') t2
        on t1.COLUMN_NAME = t2.COLUMN_NAME
        where t1.COLUMN_TYPE != t2.COLUMN_TYPE
           or t2.COLUMN_NAME is null
           or t2.COLUMN_TYPE is null
        order by t1.COLUMN_NAME
    `, srcTable, destTable)

	zlog.Debug("【差异详情】执行详细差异查询")
	differences, err := conn.ReadSql(detailSql)
	if err != nil {
		zlog.Error("【差异详情】查询详细差异失败:", err.Error())
		return
	}

	zlog.Info("【差异详情】找到", len(differences), "个具体差异:")
	for i, diff := range differences {
		actionType := diff["action_type"]
		srcColumn := diff["src_column"]
		srcType := diff["src_type"]
		destColumn := diff["dest_column"]
		destType := diff["dest_type"]

		zlog.Info("【差异详情】差异", i+1, ":")
		zlog.Info("【差异详情】  - 操作类型:", actionType)
		zlog.Info("【差异详情】  - 源表字段:", srcColumn, "(", srcType, ")")
		if destColumn != "" {
			zlog.Info("【差异详情】  - 目标表字段:", destColumn, "(", destType, ")")
		} else {
			zlog.Info("【差异详情】  - 目标表字段: 不存在")
		}

		// 特别标记新增的字段
		if actionType == "ADD" {
			zlog.Info("【差异详情】  *** 这是一个新增字段: ", srcColumn, " ***")
		}
	}

	zlog.Info("【差异详情】==================== 详细差异信息记录完成 ====================")
}

// updateTmpTableFromDDL 从DDL更新临时表结构
func (conn *Conn) updateTmpTableFromDDL(tmpTableName, mainTableSchema string) bool {
	zlog.Info("【临时表更新】开始更新临时表结构:", tmpTableName)

	// 1. 删除现有临时表
	zlog.Debug("【临时表更新】删除现有临时表:", tmpTableName)
	if !conn.DropTable(tmpTableName) {
		zlog.Warn("【临时表更新】删除临时表失败，但继续执行:", tmpTableName)
	}

	// 2. 重新创建临时表
	zlog.Debug("【临时表更新】重新创建临时表:", tmpTableName)
	if !conn.createTmpTableFromDDL(tmpTableName, mainTableSchema) {
		zlog.Error("【临时表更新】重新创建临时表失败:", tmpTableName)
		return false
	}

	zlog.Info("【临时表更新】临时表更新成功:", tmpTableName)
	return true
}

// checkTmpVsArchiveTable 检查临时表与归档表是否有差异
func (conn *Conn) checkTmpVsArchiveTable(tmpTableName, archiveTable string) bool {
	zlog.Info("【归档表差异检查】🎯 ==================== 开始归档表差异检查 ====================")
	zlog.Info("【归档表差异检查】临时表:", tmpTableName)
	zlog.Info("【归档表差异检查】归档表:", archiveTable)

	// 🎯 新增：先检查字段数量
	tmpFieldCount := conn.getTableFieldCount(tmpTableName)
	archiveFieldCount := conn.getTableFieldCount(archiveTable)

	zlog.Info("【归档表差异检查】字段数量对比:")
	zlog.Info("【归档表差异检查】  - 临时表字段数:", tmpFieldCount)
	zlog.Info("【归档表差异检查】  - 归档表字段数:", archiveFieldCount)

	if tmpFieldCount != archiveFieldCount {
		zlog.Warn("【归档表差异检查】⚠️  字段数量不一致！临时表:", tmpFieldCount, "归档表:", archiveFieldCount)
	} else {
		zlog.Info("【归档表差异检查】✅ 字段数量一致")
	}

	// 直接对比临时表与归档表结构
	zlog.Info("【归档表差异检查】开始详细结构对比...")
	hasDiff := conn.hasStructureDifference(tmpTableName, archiveTable)

	zlog.Info("【归档表差异检查】🎯 最终差异检查结果:", hasDiff)
	if hasDiff {
		zlog.Info("【归档表差异检查】✅ 检测到临时表与归档表有差异，需要同步归档表:", archiveTable)
	} else {
		zlog.Info("【归档表差异检查】⚠️  临时表与归档表结构一致，无需同步:", archiveTable)
	}

	zlog.Info("【归档表差异检查】🎯 ==================== 归档表差异检查完成 ====================")
	return hasDiff
}

// getTableFieldCount 获取表的字段数量
func (conn *Conn) getTableFieldCount(tableName string) int {
	dbName := Mysqldb.Database
	sql := fmt.Sprintf(`
		SELECT COUNT(*) as field_count
		FROM information_schema.COLUMNS
		WHERE table_schema = '%s' AND table_name = '%s'
	`, dbName, tableName)

	zlog.Info("【字段计数】🎯 执行字段计数查询，表名:", tableName)
	zlog.Debug("【字段计数】SQL:", sql)

	result, err := conn.ReadSql(sql)
	if err != nil {
		zlog.Error("【字段计数】获取表字段数量失败，表名:", tableName, "错误:", err.Error())
		return 0
	}

	if len(result) == 0 {
		zlog.Warn("【字段计数】表不存在或无字段，表名:", tableName)
		return 0
	}

	countStr := result[0]["field_count"]
	count, err := strconv.Atoi(countStr)
	if err != nil {
		zlog.Error("【字段计数】字段数量转换失败，表名:", tableName, "值:", countStr, "错误:", err.Error())
		return 0
	}

	zlog.Info("【字段计数】🎯 字段计数结果，表名:", tableName, "字段数:", count)
	return count
}

// 查询是否锁表
func (conn *Conn) Lock(tablename string) bool {
	if rs, err := conn.ReadSql("show OPEN TABLES where In_use > 0;"); err != nil {
		zlog.Error("查询锁表失败", err.Error())
	} else {
		for _, row := range rs {
			if string(row["Table"]) == tablename {
				zlog.Debug("锁表查询", row["Table"], tablename)
				return true
			}
		}
	}
	return false
}

// 从缓存中查询表结构
func (conn *Conn) Tableschme(tablename string) string {
	zlog.Debug("【Tableschme】==================== 开始查询表结构 ====================")
	zlog.Debug("【Tableschme】步骤1: 进入Tableschme方法")
	zlog.Debug("【Tableschme】查询表名:", tablename)

	zlog.Debug("【Tableschme】步骤2: 从缓存中查找表结构")
	if body, ok := Role.Load(tablename); ok {
		// 检查类型，确保是[]byte类型的表结构数据
		if bodyBytes, isByteSlice := body.([]byte); isByteSlice {
			schema := string(bodyBytes)
			zlog.Debug("【Tableschme】步骤2: 从缓存获取表结构成功 ✓")
			zlog.Debug("【Tableschme】表结构长度:", len(schema), "字节")

			// 显示表结构的关键信息
			if strings.Contains(schema, "CREATE TABLE") {
				zlog.Debug("【Tableschme】表结构类型: CREATE TABLE语句")
			}
			if len(schema) > 100 {
				zlog.Debug("【Tableschme】表结构预览:", schema[:100], "...")
			} else {
				zlog.Debug("【Tableschme】完整表结构:", schema)
			}

			zlog.Debug("【Tableschme】步骤3: 返回表结构成功")
			zlog.Debug("【Tableschme】==================== 查询表结构成功 ====================")
			return schema
		} else {
			// 缓存中的值不是[]byte类型（可能是验证标记等字符串类型）
			zlog.Error("【Tableschme】步骤2: 缓存中的值类型错误 ✗")
			zlog.Error("【Tableschme】失败原因: 键", tablename, "对应的值不是表结构数据（[]byte类型）")
			zlog.Error("【Tableschme】实际类型:", fmt.Sprintf("%T", body))
			zlog.Error("【Tableschme】可能是验证标记或其他非表结构数据")
		}
	} else {
		zlog.Error("【Tableschme】步骤2: 从缓存获取表结构失败 ✗")
		zlog.Error("【Tableschme】失败原因: 表名", tablename, "不存在于缓存中")

		// 显示缓存中所有可用的表名
		values := utils.Keys_(&Role)
		zlog.Error("【Tableschme】缓存中可用的表名:", values)

		Re.Status = TableSchmeFail
		zlog.Error("【Tableschme】设置状态为TableSchmeFail")
		zlog.Error("【Tableschme】步骤3: 返回空字符串")
		zlog.Error("【Tableschme】==================== 查询表结构失败 ====================")
	}
	return ""
}

// 检查数据库连接是否可用
func (conn *Conn) IsConnected() bool {
	if conn.Db == nil {
		return false
	}
	if err := conn.Db.Ping(); err != nil {
		return false
	}
	return true
}

func Init(mysqlbase MysqlBase) (*sql.DB, error) {
	// 检查zlog是否已初始化，避免panic
	if zlog.Sugar != nil {
		zlog.Debug("进入Init方法，初始化数据库连接")
		zlog.Debug("数据库连接参数 - 主机:", mysqlbase.Ipaddr, "端口:", mysqlbase.Port, "数据库:", mysqlbase.Database, "用户:", mysqlbase.Username)
	}
	var err error
	if DB, err = InitConn(mysqlbase); err != nil {
		Re.Status = InitDatabase
		if zlog.Sugar != nil {
			zlog.Error("初始化数据库连接失败，错误:", err.Error())
		}
		return nil, err
	}
	if zlog.Sugar != nil {
		zlog.Debug("数据库连接初始化成功")
	}
	return DB, err
}

// 开关binlog
func (conn *Conn) setBinlog(enabled bool) error {
	var err error
	if enabled {
		_, err = conn.Db.Exec("SET sql_log_bin = 1")
	} else {
		_, err = conn.Db.Exec("SET sql_log_bin = 0")
	}
	return err
}

func (conn *Conn) Close() {
	if err := DB.Close(); err != nil {
		Re.Status = ConnClose
		zlog.Error("连接关闭失败:", err.Error())
		return
	}
}

type Index struct {
	Name   string
	Column string
}

// 获取指定表的所有索引
func (conn *Conn) getIndexes(db *sql.DB, dbName, tableName string) ([]Index, error) {
	indexes := make([]Index, 0)

	rows, err := db.Query(fmt.Sprintf(`
        SELECT index_name, column_name
        FROM information_schema.statistics
        WHERE table_schema = '%s' AND table_name = '%s'
    `, dbName, tableName))
	if err != nil {
		return indexes, err
	}
	defer rows.Close()

	for rows.Next() {
		var index Index
		if err := rows.Scan(&index.Name, &index.Column); err != nil {
			return indexes, err
		}
		indexes = append(indexes, index)
	}

	return indexes, nil
}

// 比较两个索引切片是否相同
func compareIndexes(aIndexes, bIndexes []Index) bool {
	if len(aIndexes) != len(bIndexes) {
		return false
	}

	for i := range aIndexes {
		if aIndexes[i].Name != bIndexes[i].Name || aIndexes[i].Column != bIndexes[i].Column {
			return false
		}
	}

	return true
}

// 修改表索引
func (conn *Conn) AlterIndex(SrcName string, DestTable string) bool {
	AlterColumns := map[string]any{}
	// 1 找出不一样的字段
	sqlstr := fmt.Sprintf(`
    select
        t1.TABLE_NAME as t1_TABLE_NAME,
        t1.INDEX_NAME as t1_INDEX_NAME,
        t1.COLUMN_NAME as t1_COLUMN_NAME,
        t2.COLUMN_NAME as t2_COLUMN_NAME
    from
        (
        select
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME
        from
            INFORMATION_SCHEMA.STATISTICS
        where
            TABLE_SCHEMA = 'gstation'
            and TABLE_NAME = '%s') t1
    left join
            (
        select
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME
        from
            INFORMATION_SCHEMA.STATISTICS
        where
            TABLE_SCHEMA = 'gstation'
            and TABLE_NAME = '%s') t2
            on
        t1.COLUMN_NAME = t2.COLUMN_NAME
    where
        t2.INDEX_NAME is null
`, SrcName, DestTable)
	zlog.Debug(sqlstr)
	diffdf, err := conn.ReadSql(sqlstr)
	zlog.Debug(diffdf)
	if err != nil {
		zlog.Debug(err.Error())
	}
	// 遍历出字段名或者类型不同的字段信息
	for _, v := range diffdf {
		zlog.Debug(v)
		if colume, ok = v["t2_COLUMN_NAME"]; ok {
			if columetype, ok = v["t2_COLUMN_TYPE"]; ok {
				if len(columetype) < 1 {
					// ADD
					ActionNum = 1
				}
				if len(columetype) >= 1 {
					// CHANGE
					ActionNum = 2
				}
			}
			AlterColumns[v["t1_COLUMN_NAME"]] = ActionNum
		}
	}
	// 5 执行sql
	zlog.Debug("执行同步表结构sql1", SrcName, DestTable, AlterColumns)
	for _, Sqlstr := range conn.ActionSql(DestTable, AlterColumns) {
		// 验证
		zlog.Debug("执行同步表结构sql2", Sqlstr)
		if _, err := conn.Db.Exec(Sqlstr); err != nil {
			zlog.Debug(err.Error())
			return false
		}
	}
	return true
}

// 获取动作sql
func (conn *Conn) ActionIndexSql(DestTable string, AlterColumns map[string]any) []string {
	var ActionSqls []string
	if body, ok := Role.Load(DestTable); ok {
		// 检查类型，确保是[]byte类型的表结构数据
		if bodyBytes, isByteSlice := body.([]byte); isByteSlice {
			newbody := strings.Split(string(bodyBytes), "\n")
			for _, i := range newbody {
				for ColumeName, ActionNum := range AlterColumns {
					// 包含列名且不包含索引名
					if strings.Contains(i, fmt.Sprintf("`%s`", ColumeName)) && !strings.Contains(i, "KEY") {
						zlog.Debug(ColumeName, ActionNum)
						switch ActionNum.(int64) {
						// 增加字段
						case 1:
							ActionSqls = append(ActionSqls, conn.RemoveComma(fmt.Sprintf("ALTER TABLE %s ADD %s", DestTable, i)))
							zlog.Debug(conn.RemoveComma(fmt.Sprintf("ALTER TABLE %s ADD %s", DestTable, i)))
						// 修改字段
						case 2:
							ActionSqls = append(ActionSqls, conn.RemoveComma(fmt.Sprintf("ALTER TABLE %s CHANGE %s %s", DestTable, ColumeName, i)))
							zlog.Debug(conn.RemoveComma(fmt.Sprintf("ALTER TABLE %s CHANGE %s %s", DestTable, ColumeName, i)))
						default:
							zlog.Debug("no action match")
						}
					}
				}
			}
			return ActionSqls
		} else {
			// 缓存中的值不是[]byte类型（可能是验证标记等字符串类型）
			zlog.Error("【ActionIndexSql】缓存中的值类型错误")
			zlog.Error("【ActionIndexSql】键", DestTable, "对应的值不是表结构数据（[]byte类型）")
			zlog.Error("【ActionIndexSql】实际类型:", fmt.Sprintf("%T", body))
		}
	}
	return nil
}

// 数据库连接检查和维护
func (conn *Conn) LoopConn() {
	// 如果数据库连接为nil，说明还未建立连接，跳过检查
	if conn.Db == nil {
		zlog.Debug("数据库连接未建立，跳过连接检查")
		return
	}

	// 检查现有连接状态
	if err := conn.Db.Ping(); err != nil {
		Re.Status = DataBaseConnDisconnect
		zlog.Warn("数据库连接断开，尝试重连，错误:", err.Error())

		// 尝试重连，失败时不频繁重试
		if newDb, err := Init(*Mysqldb); err != nil {
			Re.Status = ReConnDatabase
			zlog.Error("数据库重连失败，将在下次检查时重试，错误:", err.Error())
			// 连接失败时将连接设为nil，避免后续操作使用无效连接
			conn.Db = nil
		} else {
			conn.Db = newDb
			Re.Status = 0
			zlog.Info("数据库重连成功")
		}
	} else {
		// 连接正常时，只在debug模式下输出日志，避免日志过多
		if Re.DebugLog {
			zlog.Debug("数据库连接状态正常")
		}
		Re.Status = 0
	}
}

// 查看表归档比例
func (conn *Conn) CalculateTableArchivalProgress(tbName, dbName string) float64 {
	sql := fmt.Sprintf("SELECT TABLE_NAME, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME LIKE '%s%%'", dbName, tbName)
	df, err := conn.ReadSql(sql)
	if err != nil {
		fmt.Println(err.Error())
		return 0.0
	}

	var totalRows float64
	for _, row := range df {
		if rowCountStr, ok := row["TABLE_ROWS"]; ok {
			rowCount, err := strconv.ParseFloat(rowCountStr, 64)
			if err == nil {
				totalRows += rowCount
			}
		}
	}

	if totalRows > 0 {
		if tbRowCountStr, ok := df[0]["TABLE_ROWS"]; ok {
			tbRowCount, err := strconv.ParseFloat(tbRowCountStr, 64)
			if err == nil {
				// 总数 - 当前表数 / 总数
				result := ((totalRows - tbRowCount) / totalRows) * 100

				resultString := strconv.FormatFloat(result, 'f', 2, 64) // Format the result with two decimal places
				result, _ = strconv.ParseFloat(resultString, 64)        // Convert the formatted result back to float64
				return result
			}
		}
	}

	return 0.0
}

func (conn *Conn) TaskProgress(tableau, dbName string) float64 {
	return conn.CalculateTableArchivalProgress(tableau, dbName)
}
